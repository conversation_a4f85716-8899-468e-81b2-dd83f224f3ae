// AI模型配置文件
import { SUPPORTED_MODELS, getModelInfo } from '@/services/pollinationsApi.js'

// 延迟导入OpenRouter模型列表，避免在模块加载时初始化网络功能
let _openrouterModels = null
const getOpenRouterModels = async () => {
  if (!_openrouterModels) {
    try {
      const { OPENROUTER_FREE_MODELS } = await import('@/services/openrouterApi.js')
      _openrouterModels = OPENROUTER_FREE_MODELS
    } catch (error) {
      console.warn('无法加载OpenRouter模型列表:', error)
      _openrouterModels = []
    }
  }
  return _openrouterModels
}

// 延迟导入Targon模型列表
let _targonModels = null
const getTargonModels = async () => {
  if (!_targonModels) {
    try {
      const targonApi = await import('@/services/targonApi.js')
      const result = await targonApi.default.getModels()
      if (result.success && result.data) {
        _targonModels = result.data.map(model => model.id)
      } else {
        // 使用预设的 Targon 模型列表作为备用
        _targonModels = [
          'THUDM/GLM-4.5-Air',
          'THUDM/GLM-4.5',
          'THUDM/GLM-4-Plus',
          'deepseek-ai/DeepSeek-V3',
          'deepseek-ai/DeepSeek-R1',
          'deepseek-ai/deepseek-chat',
          'moonshot/Kimi-K2-Instruct',
          'moonshot/moonshot-v1-8k',
          'Qwen/Qwen3-Coder-Instruct',
          'Qwen/Qwen3-235B-Instruct',
          'Qwen/QwQ-32B-Preview',
          'OpenRouter/Auto'
        ]
      }
    } catch (error) {
      console.warn('无法加载Targon模型列表:', error)
      // 使用预设的模型列表
      _targonModels = [
        'THUDM/GLM-4.5-Air',
        'THUDM/GLM-4.5',
        'deepseek-ai/DeepSeek-V3',
        'deepseek-ai/DeepSeek-R1',
        'deepseek-ai/deepseek-chat',
        'moonshot/Kimi-K2-Instruct',
        'Qwen/Qwen3-Coder-Instruct'
      ]
    }
  }
  return _targonModels
}

export const AI_MODEL_CATEGORIES = [
  { id: 'all', name: '全部', icon: '🤖' },
  { id: 'doubao', name: '豆包 AI', icon: '🎯' },
  { id: 'zhipu', name: '智谱 AI', icon: '🧠' },
  { id: 'anthropic', name: 'Anthropic', icon: '🎭' },
  { id: 'targon', name: 'Targon', icon: '🎯' },
  { id: 'openai', name: 'OpenAI', icon: '🔥' },
  { id: 'mistral', name: 'Mistral', icon: '🌟' },
  { id: 'meta', name: 'Meta/Llama', icon: '📘' },
  { id: 'google', name: 'Google', icon: '🔍' },
  { id: 'deepseek', name: 'DeepSeek', icon: '🧠' },
  { id: 'qwen', name: 'Qwen', icon: '🚀' },
  { id: 'moonshot', name: 'Moonshot', icon: '🌙' },
  { id: 'microsoft', name: 'Microsoft', icon: '💎' },
  { id: 'pollinations', name: 'Pollinations', icon: '🌸' },
  { id: 'openrouter', name: 'OpenRouter', icon: '🔗' },
  { id: 'specialized', name: '专业模型', icon: '⚙️' },
  { id: 'creative', name: '创意模型', icon: '🎨' },
  { id: 'image', name: '图像生成', icon: '🖼️' },
  { id: 'audio', name: '音频处理', icon: '🎵' }
]



// 根据模型ID确定分类
function categorizeModel(modelId) {
  const name = modelId.toLowerCase()

  // Doubao 模型识别
  if (name.includes('doubao-pro') || name.includes('doubao')) {
    return 'doubao'
  }

  // Qwen-image 模型识别
  if (name.includes('qwen-plus') || name.includes('qwen-image')) {
    return 'qwen'
  }

  // 特定模型优先识别
  if (name === 'deepseek-r1') {
    return 'deepseek'
  }
  if (name === 'openai-gpt-4.1') {
    return 'openai'
  }

  // Anthropic/Claude 模型识别
  if (name.includes('claude-3.5-sonnet') ||
      name.includes('claude-3.7-sonnet') ||
      name.includes('claude-4-sonnet') ||
      name.includes('claude-3') ||
      name.includes('anthropic')) {
    return 'anthropic'
  }

  // 智谱模型识别
  if (name.includes('glm-4.5') || name.includes('glm-4') || name.includes('zhipu')) {
    return 'zhipu'
  }

  // Moonshot 模型识别
  if (name.includes('moonshot') || name.includes('kimi')) {
    return 'moonshot'
  }

  // Targon 模型识别 (通常以组织名/模型名格式，如 deepseek-ai/DeepSeek-V3)
  if (name.includes('deepseek-ai/') ||
      name.includes('moonshot/') ||
      name.includes('qwen/') ||
      name.includes('targon:') ||
      name.startsWith('sn4_')) {
    return 'targon'
  }

  // OpenRouter 模型识别 (包含 / 和 :free 的模型)
  if (name.includes('/') && name.includes(':free')) {
    return 'openrouter'
  }

  // 根据模型名称分类
  if (name.includes('openai')) {
    return 'openai'
  }
  if (name.includes('mistral') || name.includes('unity')) {
    return 'mistral'
  }
  if (name.includes('llama')) {
    return 'meta'
  }
  if (name.includes('gemini')) {
    return 'google'
  }
  if (name.includes('deepseek')) {
    return 'deepseek'
  }
  if (name.includes('qwen')) {
    return 'qwen'
  }
  if (name.includes('phi')) {
    return 'microsoft'
  }
  if (name.includes('claude')) {
    return 'anthropic'
  }

  // 图像生成模型
  if (name.includes('flux') || name.includes('turbo')) {
    return 'image'
  }

  // 音频处理模型
  if (name.includes('midijourney') || name.includes('hypnosis') || name.includes('audio')) {
    return 'audio'
  }

  // 创意模型
  if (name.includes('rtist') || name.includes('evil') || name.includes('hormoz') || name.includes('sur')) {
    return 'creative'
  }

  // 专业模型
  if (name.includes('searchgpt') || name.includes('scout') || name.includes('vision')) {
    return 'specialized'
  }

  // Pollinations 特有模型
  if (['hormoz', 'hypnosis-tracy', 'sur', 'evil', 'rtist', 'midijourney', 'searchgpt'].includes(name)) {
    return 'pollinations'
  }

  return 'specialized'
}

// 获取模型描述 (基于最新 Pollinations.AI llms.txt)
function getModelDescription(modelId) {
  const descriptions = {
    // Doubao AI 模型
    'doubao-pro-32k': '豆包AI专业版，支持32K上下文，可进行对话和生成图片',
    
    // Qwen-image 模型
    'qwen-plus': 'Qwen Plus - 阿里云通义千问图像生成模型，支持推理过程展示',
    
    // Pollinations.AI 官方模型
    'openai': 'OpenAI GPT-4.1-nano，支持文本和图像输入',
    'openai-large': 'OpenAI GPT-4.1 mini，支持文本和图像输入',
    'openai-reasoning': 'OpenAI o4-mini，专注推理的模型',
    'openai-audio': 'OpenAI GPT-4o-audio-preview，支持文本、图像和音频',
    'qwen-coder': 'Qwen 2.5 Coder 32B，专业代码生成模型',
    'llama': 'Llama 3.3 70B，Meta最新开源模型',
    'llamascout': 'Llama 4 Scout 17B，轻量级高效模型',
    'llama-vision': 'Llama 3.2 11B Vision，支持视觉理解',
    'mistral': 'Mistral Small 3，支持文本和图像输入',
    'unity': 'Unity Mistral Large，无审查多模态模型',
    'midijourney': 'Midijourney，专注音乐生成的模型',
    'rtist': 'Rtist，专注创意写作的模型',
    'searchgpt': 'SearchGPT，搜索增强的对话模型',
    'evil': 'Evil，无审查的文本和图像模型',
    'deepseek': 'DeepSeek-V3，最新深度推理模型',
    'deepseek-reasoning': 'DeepSeek-R1 Distill Qwen 32B，推理优化',
    'deepseek-reasoning-large': 'DeepSeek R1 - Llama 70B，大型推理模型',
    'phi': 'Phi-4 Instruct，支持文本、图像和音频',
    'gemini': 'Gemini 2.5 Flash Preview，多模态输入输出',
    'hormoz': 'Hormoz 8b，专业对话助手',
    'hypnosis-tracy': 'Hypnosis Tracy 7B，支持文本和音频',
    'sur': 'Sur AI Assistant，基于Mistral的多模态助手',
    'flux': '最新稳定扩散图像生成模型',
    'turbo': '快速图像生成模型',

    // GeminiPool 支持的模型 (仅保留 2.0 和 2.5 系列)
    'gemini-2.0-flash-exp': 'Gemini 2.0实验版本',
    'gemini-2.0-flash-thinking-exp-1219': 'Gemini 2.0思维链实验版',
    'gemini-exp-1114': 'Gemini实验版本1114',
    'gemini-exp-1121': 'Gemini实验版本1121',
    'gemini-exp-1206': 'Gemini实验版本1206',

    // Gemini 2.5 系列模型
    'gemini-2.5-flash-preview-05-20': 'Gemini 2.5 Flash 预览版 05-20',
    'gemini-2.5-flash': 'Gemini 2.5 Flash 最新版本',
    'gemini-2.5-flash-lite-preview-06-17': 'Gemini 2.5 Flash Lite 预览版 06-17',
    'gemini-2.5-pro': 'Gemini 2.5 Pro 专业版本',
    'gemini-2.5-flash-lite': 'Gemini 2.5 Flash Lite 轻量版',

    // 智谱官方 API 模型
    'glm-4.5-air': 'GLM-4.5-Air，智谱官方轻量级高效模型',
    'glm-4.5': 'GLM-4.5，智谱官方标准版本模型',

    // Targon 模型系列
    // GLM 系列模型
    'THUDM/GLM-4.5-Air': 'GLM-4.5-Air，清华智谱轻量级高效模型',
    'THUDM/GLM-4.5': 'GLM-4.5，清华智谱标准版本模型',
    'THUDM/GLM-4-Plus': 'GLM-4-Plus，清华智谱增强版模型',
    // DeepSeek 系列模型
    'deepseek-ai/DeepSeek-V3': 'DeepSeek V3，最强推理能力的开源模型',
    'deepseek-ai/DeepSeek-R1': 'DeepSeek R1，专门优化的推理模型',
    'deepseek-ai/deepseek-chat': 'DeepSeek Chat，通用对话模型',
    // Moonshot 系列模型
    'moonshot/Kimi-K2-Instruct': 'Kimi K2，月之暗面指令优化模型',
    'moonshot/moonshot-v1-8k': 'Moonshot V1 8K，长文本处理模型',
    // Qwen 系列模型
    'Qwen/Qwen3-Coder-Instruct': 'Qwen 3 Coder，专业代码生成模型',
    'Qwen/Qwen3-235B-Instruct': 'Qwen 3 235B，超大规模指令模型',
    'Qwen/QwQ-32B-Preview': 'QwQ 32B，问答专用预览版模型',
    // OpenRouter 系列模型
    'OpenRouter/Auto': 'OpenRouter Auto，自动选择最佳模型'
  }

  return descriptions[modelId] || `${modelId} AI模型`
}

// 获取OpenRouter模型描述
function getOpenRouterModelDescription(modelId) {
  const descriptions = {
    'deepseek/deepseek-chat-v3-0324:free': 'DeepSeek Chat V3，最新对话模型',
    'deepseek/deepseek-r1-0528:free': 'DeepSeek R1，推理优化模型',
    'qwen/qwen3-coder:free': 'Qwen 3 Coder，专业代码生成',
    'deepseek/deepseek-r1:free': 'DeepSeek R1，深度推理模型',
    'qwen/qwen3-235b-a22b-2507:free': 'Qwen 3 235B，超大规模模型',
    'moonshotai/kimi-k2:free': 'Kimi K2，月之暗面对话模型',
    'google/gemini-2.0-flash-exp:free': 'Gemini 2.0 Flash 实验版',
    'qwen/qwq-32b:free': 'QwQ 32B，问答专用模型',
    'qwen/qwen3-14b:free': 'Qwen 3 14B，中等规模模型',
    'mistralai/mistral-nemo:free': 'Mistral Nemo，轻量级模型',
    'deepseek/deepseek-r1-0528-qwen3-8b:free': 'DeepSeek R1 Qwen 8B 混合模型',
    'microsoft/mai-ds-r1:free': 'Microsoft MAI DS R1，微软推理模型',
    'qwen/qwen3-235b-a22b:free': 'Qwen 3 235B A22B，超大模型',
    'mistralai/mistral-small-3.1-24b-instruct:free': 'Mistral Small 3.1 24B',
    'qwen/qwen2.5-vl-72b-instruct:free': 'Qwen 2.5 VL 72B，视觉语言模型',
    'mistralai/mistral-small-3.2-24b-instruct:free': 'Mistral Small 3.2 24B',
    'moonshotai/kimi-dev-72b:free': 'Kimi Dev 72B，开发者版本',
    'google/gemma-3-27b-it:free': 'Gemma 3 27B IT，Google开源模型',
    'qwen/qwen3-30b-a3b:free': 'Qwen 3 30B A3B，中大型模型',
    'qwen/qwen-2.5-coder-32b-instruct:free': 'Qwen 2.5 Coder 32B，代码专用',
    'mistralai/devstral-small-2505:free': 'Devstral Small，开发专用',
    'mistralai/mistral-7b-instruct:free': 'Mistral 7B Instruct，指令调优',
    'deepseek/deepseek-r1-distill-llama-70b:free': 'DeepSeek R1 Llama 70B 蒸馏版',
    'meta-llama/llama-3.3-70b-instruct:free': 'Llama 3.3 70B，Meta最新模型',
    'qwen/qwen3-8b:free': 'Qwen 3 8B，轻量级模型',
    'meta-llama/llama-3.2-11b-vision-instruct:free': 'Llama 3.2 11B Vision，视觉模型',
    'qwen/qwen-2.5-72b-instruct:free': 'Qwen 2.5 72B，大型指令模型',
    'thudm/glm-z1-32b:free': 'GLM Z1 32B，清华大学模型',
    'qwen/qwen2.5-vl-32b-instruct:free': 'Qwen 2.5 VL 32B，视觉语言模型',
    'tencent/hunyuan-a13b-instruct:free': 'Hunyuan A13B，腾讯混元模型',
    'google/gemma-2-9b-it:free': 'Gemma 2 9B IT，Google轻量模型',
    'google/gemma-3-12b-it:free': 'Gemma 3 12B IT，Google中型模型',
    'meta-llama/llama-3.1-405b-instruct:free': 'Llama 3.1 405B，超大规模模型',
    'mistralai/mistral-small-24b-instruct-2501:free': 'Mistral Small 24B 2025版',
    'google/gemma-3-4b-it:free': 'Gemma 3 4B IT，Google小型模型',
    'rekaai/reka-flash-3:free': 'Reka Flash 3，快速响应模型',
    'qwen/qwen3-4b:free': 'Qwen 3 4B，超轻量模型',
    'meta-llama/llama-3.2-3b-instruct:free': 'Llama 3.2 3B，轻量指令模型',
    'nvidia/llama-3.1-nemotron-ultra-253b-v1:free': 'Nemotron Ultra 253B，NVIDIA超大模型',
    'deepseek/deepseek-r1-distill-qwen-14b:free': 'DeepSeek R1 Qwen 14B 蒸馏版'
  }

  return descriptions[modelId] || `${modelId.split('/')[1]?.replace(':free', '')} - OpenRouter免费模型`
}

// 动态获取AI模型列表
export async function getAIModels() {
  try {
    // 合并Pollinations、OpenRouter、Targon、智谱、Claude、Doubao和Qwen-image模型
    const pollinationsModels = SUPPORTED_MODELS.map(modelId => transformPollinationsModel(modelId))
    const openrouterModelList = await getOpenRouterModels()
    const openrouterModels = openrouterModelList.map(modelId => transformOpenRouterModel(modelId))
    const targonModelList = await getTargonModels()
    const targonModels = targonModelList.map(modelId => transformTargonModel(modelId))
    const zhipuModels = getZhipuModels()
    const claudeModels = getClaudeModels()
    const doubaoModels = getDoubaoModels()
    const qwenImageModels = getQwenImageModels()

    return [...pollinationsModels, ...openrouterModels, ...targonModels, ...zhipuModels, ...claudeModels, ...doubaoModels, ...qwenImageModels]
  } catch (error) {
    console.error('获取AI模型失败:', error)
    return getStaticModels()
  }
}

// 转换Pollinations模型格式
function transformPollinationsModel(modelId) {
  const modelInfo = getModelInfo(modelId)
  const category = categorizeModel(modelId)

  return {
    id: modelId,
    name: modelInfo.name,
    description: getModelDescription(modelId),
    category: category,
    status: 'online', // Pollinations的模型都是在线的
    parameters: getModelParameters(modelId),
    speed: getModelSpeed(modelId),
    quality: getModelQuality(modelId),
    capabilities: getModelCapabilities(modelId),
    isPremium: isPremiumModel(modelId),
    isNew: isNewModel(modelId),
    isFast: isFastModel(modelId),
    isFree: !isPremiumModel(modelId),
    pricing: getModelPricing(modelId),
    usageCount: Math.floor(Math.random() * 5000), // 模拟使用次数
    contextLength: getContextLength(modelId),
    multimodal: modelInfo.capabilities.includes('vision'),
    provider: modelInfo.provider,
    tools: modelInfo.capabilities.includes('tools'),
    vision: modelInfo.capabilities.includes('vision'),
    audio: modelInfo.capabilities.includes('audio'),
    reasoning: modelInfo.capabilities.includes('reasoning'),
    coding: modelInfo.capabilities.includes('code')
  }
}

// 转换OpenRouter模型格式
function transformOpenRouterModel(modelId) {
  const parts = modelId.split('/')
  const provider = parts[0]
  const modelName = parts[1]?.replace(':free', '') || modelId

  // 提取模型参数信息
  const getParametersFromName = (name) => {
    if (name.includes('405b')) return '405B'
    if (name.includes('70b')) return '70B'
    if (name.includes('72b')) return '72B'
    if (name.includes('32b')) return '32B'
    if (name.includes('27b')) return '27B'
    if (name.includes('24b')) return '24B'
    if (name.includes('14b')) return '14B'
    if (name.includes('12b')) return '12B'
    if (name.includes('11b')) return '11B'
    if (name.includes('9b')) return '9B'
    if (name.includes('8b')) return '8B'
    if (name.includes('7b')) return '7B'
    if (name.includes('4b')) return '4B'
    if (name.includes('3b')) return '3B'
    return '未知'
  }

  // 获取模型质量评分
  const getQualityScore = (name) => {
    if (name.includes('405b') || name.includes('ultra')) return 5
    if (name.includes('70b') || name.includes('72b')) return 4
    if (name.includes('32b') || name.includes('30b')) return 4
    if (name.includes('27b') || name.includes('24b')) return 3
    if (name.includes('14b') || name.includes('12b')) return 3
    if (name.includes('9b') || name.includes('8b')) return 3
    if (name.includes('7b') || name.includes('4b')) return 2
    if (name.includes('3b')) return 2
    return 3
  }

  // 获取速度评分
  const getSpeedScore = (name) => {
    if (name.includes('3b') || name.includes('4b')) return 95
    if (name.includes('7b') || name.includes('8b')) return 85
    if (name.includes('9b') || name.includes('12b')) return 75
    if (name.includes('14b') || name.includes('24b')) return 65
    if (name.includes('27b') || name.includes('30b')) return 55
    if (name.includes('32b')) return 50
    if (name.includes('70b') || name.includes('72b')) return 40
    if (name.includes('405b')) return 25
    return 70
  }

  // 判断是否支持视觉
  const hasVision = modelName.includes('vision') || modelName.includes('vl')

  // 判断是否为新模型
  const isNew = modelName.includes('2025') || modelName.includes('2024') ||
                modelName.includes('r1') || modelName.includes('v3') ||
                modelName.includes('gemini-2.0') || modelName.includes('qwen3')

  return {
    id: modelId,
    name: `${provider}/${modelName}`,
    description: getOpenRouterModelDescription(modelId),
    category: 'openrouter',
    status: 'online',
    parameters: getParametersFromName(modelName),
    speed: getSpeedScore(modelName),
    quality: getQualityScore(modelName),
    capabilities: hasVision ? ['对话', '视觉理解'] : ['对话'],
    isPremium: false,
    isNew: isNew,
    isFast: getSpeedScore(modelName) > 80,
    isFree: true,
    pricing: '免费',
    usageCount: Math.floor(Math.random() * 3000),
    contextLength: hasVision ? '32K' : '128K',
    multimodal: hasVision,
    provider: 'OpenRouter',
    tools: false,
    vision: hasVision,
    audio: false,
    tier: 'free'
  }
}

// 转换Targon模型格式
function transformTargonModel(modelId) {
  const parts = modelId.split('/')
  const provider = parts[0] || 'targon'
  const modelName = parts[1] || modelId

  // 获取模型参数信息
  const getTargonParameters = (name) => {
    if (name.includes('GLM-4.5-Air')) return '9B'
    if (name.includes('GLM-4.5')) return '9B'
    if (name.includes('GLM-4-Plus')) return '9B'
    if (name.includes('V3') || name.includes('235B')) return '235B+'
    if (name.includes('R1') || name.includes('32B')) return '32B'
    if (name.includes('K2') || name.includes('8k')) return '8B'
    if (name.includes('Coder')) return '14B'
    return '未知'
  }

  // 获取模型质量评分
  const getTargonQuality = (name) => {
    if (name.includes('GLM-4.5')) return 4
    if (name.includes('GLM-4-Plus')) return 4
    if (name.includes('V3') || name.includes('235B')) return 5
    if (name.includes('R1') || name.includes('K2')) return 4
    if (name.includes('Coder') || name.includes('QwQ')) return 4
    return 4
  }

  // 获取模型速度评分
  const getTargonSpeed = (name) => {
    if (name.includes('GLM-4.5-Air')) return 5 // 轻量级，速度最快
    if (name.includes('GLM-4.5')) return 4 // 标准速度
    if (name.includes('GLM-4-Plus')) return 3 // 增强版，相对较慢
    if (name.includes('V3')) return 3 // 高质量但相对较慢
    if (name.includes('R1')) return 4 // 推理优化
    if (name.includes('K2')) return 4 // 快速响应
    if (name.includes('Coder')) return 3 // 代码生成
    return 4
  }

  // 获取模型能力
  const getTargonCapabilities = (name) => {
    const capabilities = ['chat', 'text']
    if (name.includes('GLM')) capabilities.push('reasoning', 'analysis')
    if (name.includes('Coder')) capabilities.push('code')
    if (name.includes('R1') || name.includes('QwQ')) capabilities.push('reasoning')
    if (name.includes('V3')) capabilities.push('reasoning', 'analysis')
    return capabilities
  }

  return {
    id: modelId,
    name: modelName,
    description: getModelDescription(modelId),
    category: 'targon',
    status: 'online',
    parameters: getTargonParameters(modelName),
    speed: getTargonSpeed(modelName),
    quality: getTargonQuality(modelName),
    capabilities: getTargonCapabilities(modelName),
    isPremium: false, // 使用密钥池，对用户免费
    isNew: modelName.includes('V3') || modelName.includes('R1') || modelName.includes('GLM-4.5'),
    isFast: getTargonSpeed(modelName) >= 4,
    isFree: true, // 使用内置密钥池
    pricing: '免费 (密钥池)',
    usageCount: Math.floor(Math.random() * 8000), // Targon模型使用量较高
    contextLength: modelName.includes('8k') ? '8K' : (modelName.includes('32B') ? '32K' : '16K'),
    multimodal: false, // 目前Targon主要是文本模型
    provider: provider,
    tools: getTargonCapabilities(modelName).includes('tools'),
    vision: false,
    audio: false,
    reasoning: getTargonCapabilities(modelName).includes('reasoning'),
    coding: getTargonCapabilities(modelName).includes('code'),
    keyPool: true, // 标识使用密钥池
    tier: 'premium' // 高质量模型
  }
}

// 获取模型参数规模
function getModelParameters(modelId) {
  const parameterMap = {
    // Pollinations.AI 官方模型
    'openai': '未知',
    'openai-large': '未知',
    'openai-reasoning': '未知',
    'openai-audio': '未知',
    'qwen-coder': '32B',
    'llama': '70B',
    'llamascout': '17B',
    'llama-vision': '11B',
    'mistral': '未知',
    'unity': '大型',
    'midijourney': '未知',
    'rtist': '未知',
    'searchgpt': '未知',
    'evil': '未知',
    'deepseek': '未知',
    'deepseek-reasoning': '32B',
    'deepseek-reasoning-large': '70B',
    'phi': '未知',
    'gemini': '未知',
    'hormoz': '8B',
    'hypnosis-tracy': '7B',
    'sur': '未知',
    'flux': '未知',
    'turbo': '未知',

    // GeminiPool 模型参数 (仅保留 2.0 和 2.5 系列)
    'gemini-2.0-flash-exp': '超大型',
    'gemini-2.0-flash-thinking-exp-1219': '超大型',
    'gemini-exp-1114': '大型',
    'gemini-exp-1121': '大型',
    'gemini-exp-1206': '大型',

    // Gemini 2.5 系列模型参数
    'gemini-2.5-flash-preview-05-20': '大型',
    'gemini-2.5-flash': '大型',
    'gemini-2.5-flash-lite-preview-06-17': '中型',
    'gemini-2.5-pro': '超大型',
    'gemini-2.5-flash-lite': '中型'
  }
  return parameterMap[modelId] || '未知'
}

// 获取模型速度评分
function getModelSpeed(modelId) {
  const speedMap = {
    'openai': 95,
    'openai-large': 70,
    'openai-reasoning': 50,
    'qwen-coder': 80,
    'llama': 90,
    'mistral': 85,
    'deepseek': 75,
    'deepseek-r1': 60,
    'deepseek-reasoner': 55,
    // GeminiPool 模型速度 (仅保留 2.0 和 2.5 系列)
    'gemini-2.0-flash-exp': 85,
    'gemini-2.0-flash-thinking-exp-1219': 60,
    'gemini-exp-1114': 80,
    'gemini-exp-1121': 80,
    'gemini-exp-1206': 80,

    // Gemini 2.5 系列模型速度
    'gemini-2.5-flash-preview-05-20': 95,
    'gemini-2.5-flash': 95,
    'gemini-2.5-flash-lite-preview-06-17': 98,
    'gemini-2.5-pro': 75,
    'gemini-2.5-flash-lite': 98,
    'llamalight': 98,
    'llamaguard': 90
  }
  return speedMap[modelId] || 80
}

// 获取模型质量评分
function getModelQuality(modelId) {
  const qualityMap = {
    'openai': 4,
    'openai-large': 5,
    'openai-reasoning': 5,
    'qwen-coder': 4,
    'llama': 4,
    'mistral': 4,
    'deepseek': 5,
    'deepseek-r1': 5,
    'deepseek-reasoner': 5,
    // GeminiPool 模型质量 (仅保留 2.0 和 2.5 系列)
    'gemini-2.0-flash-exp': 5,
    'gemini-2.0-flash-thinking-exp-1219': 5,
    'gemini-exp-1114': 4,
    'gemini-exp-1121': 4,
    'gemini-exp-1206': 4,

    // Gemini 2.5 系列模型质量
    'gemini-2.5-flash-preview-05-20': 5,
    'gemini-2.5-flash': 5,
    'gemini-2.5-flash-lite-preview-06-17': 4,
    'gemini-2.5-pro': 5,
    'gemini-2.5-flash-lite': 4,
    'llamalight': 3,
    'llamaguard': 3
  }
  return qualityMap[modelId] || 4
}

// 获取模型能力标签
function getModelCapabilities(modelId) {
  const capabilityMap = {
    // Pollinations.AI 官方模型
    'openai': ['对话', '图像理解'],
    'openai-large': ['对话', '图像理解'],
    'openai-reasoning': ['对话', '推理'],
    'openai-audio': ['对话', '图像理解', '音频处理'],
    'qwen-coder': ['对话', '编程', '代码生成'],
    'llama': ['对话', '多语言'],
    'llamascout': ['对话', '快速响应'],
    'llama-vision': ['对话', '图像理解'],
    'mistral': ['对话', '图像理解'],
    'unity': ['对话', '图像理解', '无审查'],
    'midijourney': ['音乐创作', '音频生成'],
    'rtist': ['创意写作', '艺术创作'],
    'searchgpt': ['对话', '搜索增强'],
    'evil': ['对话', '图像理解', '无审查'],
    'deepseek': ['对话', '推理'],
    'deepseek-reasoning': ['对话', '推理', '分析'],
    'deepseek-reasoning-large': ['对话', '推理', '分析'],
    'phi': ['对话', '图像理解', '音频处理'],
    'gemini': ['对话', '图像理解', '音频处理'],
    'hormoz': ['对话', '专业助手'],
    'hypnosis-tracy': ['对话', '音频处理', '心理学'],
    'sur': ['对话', '图像理解'],
    'flux': ['图像生成', '稳定扩散'],
    'turbo': ['图像生成', '快速生成'],

    // GeminiPool 模型能力 (仅保留 2.0 和 2.5 系列)
    'gemini-2.0-flash-exp': ['对话', '图像理解', '推理', '实验性'],
    'gemini-2.0-flash-thinking-exp-1219': ['对话', '推理', '思维链', '实验性'],
    'gemini-exp-1114': ['对话', '图像理解', '实验性'],
    'gemini-exp-1121': ['对话', '图像理解', '实验性'],
    'gemini-exp-1206': ['对话', '图像理解', '实验性'],

    // Gemini 2.5 系列模型能力
    'gemini-2.5-flash-preview-05-20': ['对话', '图像理解', '推理', '预览版'],
    'gemini-2.5-flash': ['对话', '图像理解', '推理', '快速响应'],
    'gemini-2.5-flash-lite-preview-06-17': ['对话', '图像理解', '轻量级', '预览版'],
    'gemini-2.5-pro': ['对话', '图像理解', '推理', '编程', '高级功能'],
    'gemini-2.5-flash-lite': ['对话', '图像理解', '轻量级', '快速响应']
  }
  return capabilityMap[modelId] || ['对话']
}

// 判断是否为新模型
function isNewModel(modelId) {
  const newModels = [
    // Pollinations.AI 新模型
    'openai-reasoning', 'openai-audio', 'llamascout', 'llama-vision', 'phi',
    'deepseek-reasoning', 'deepseek-reasoning-large', 'gemini', 'flux', 'turbo',

    // GeminiPool 新模型 (仅保留 2.0 和 2.5 系列)
    'gemini-2.0-flash-exp', 'gemini-2.0-flash-thinking-exp-1219',
    'gemini-exp-1114', 'gemini-exp-1121', 'gemini-exp-1206',

    // Gemini 2.5 系列新模型
    'gemini-2.5-flash-preview-05-20', 'gemini-2.5-flash', 'gemini-2.5-flash-lite-preview-06-17',
    'gemini-2.5-pro', 'gemini-2.5-flash-lite'
  ]
  return newModels.includes(modelId)
}

// 判断是否为快速模型
function isFastModel(modelId) {
  const fastModels = [
    // Pollinations.AI 快速模型
    'openai', 'llamascout', 'turbo', 'phi',

    // GeminiPool 快速模型 (仅保留 2.5 系列快速模型)
    'gemini-2.5-flash-preview-05-20', 'gemini-2.5-flash', 'gemini-2.5-flash-lite-preview-06-17',
    'gemini-2.5-flash-lite'
  ]
  return fastModels.includes(modelId)
}

// 判断是否为付费模型
function isPremiumModel(modelId) {
  const premiumModels = [
    'openai-large', 'openai-reasoning', 'deepseek-r1',
    'gemini-2.5-pro'
  ]
  return premiumModels.includes(modelId)
}

// 获取模型定价
function getModelPricing(modelId) {
  return isPremiumModel(modelId) ? '付费' : '免费'
}

// 获取上下文长度
function getContextLength(modelId) {
  const contextMap = {
    'openai': '128K',
    'openai-large': '128K',
    'openai-reasoning': '32K',
    'qwen-coder': '32K',
    'llama': '8K',
    'mistral': '32K',
    'deepseek': '64K',
    'deepseek-r1': '64K',
    'deepseek-reasoner': '64K',
    // GeminiPool 模型上下文长度 (仅保留 2.0 和 2.5 系列)
    'gemini-2.0-flash-exp': '1M',
    'gemini-2.0-flash-thinking-exp-1219': '32K',
    'gemini-exp-1114': '1M',
    'gemini-exp-1121': '1M',
    'gemini-exp-1206': '1M',

    // Gemini 2.5 系列模型上下文长度
    'gemini-2.5-flash-preview-05-20': '1M',
    'gemini-2.5-flash': '1M',
    'gemini-2.5-flash-lite-preview-06-17': '1M',
    'gemini-2.5-pro': '2M',
    'gemini-2.5-flash-lite': '1M'
  }
  return contextMap[modelId] || '8K'
}

// 静态模型列表（作为备用）
function getStaticModels() {
  return [
    {
      id: 'openai',
      name: 'OpenAI GPT-4o Mini',
      description: '快速高效的对话模型，支持视觉理解',
      category: 'openai',
      status: 'online',
      parameters: '小型',
      speed: 95,
      quality: 4,
      capabilities: ['对话', '图像理解', '工具调用'],
      isPremium: false,
      isNew: false,
      isFast: true,
      isFree: true,
      pricing: '免费',
      usageCount: 5670,
      contextLength: '4K',
      multimodal: true,
      provider: 'azure',
      tier: 'anonymous',
      tools: true,
      vision: true,
      audio: false
    },
    {
      id: 'mistral',
      name: 'Mistral Small 3.1 24B',
      description: '强大的开源模型，支持多模态',
      category: 'mistral',
      status: 'online',
      parameters: '24B',
      speed: 85,
      quality: 4,
      capabilities: ['对话', '图像理解', '工具调用'],
      isPremium: false,
      isNew: true,
      isFast: false,
      isFree: true,
      pricing: '免费',
      usageCount: 3240,
      contextLength: '4K',
      multimodal: true,
      provider: 'cloudflare',
      tier: 'anonymous',
      tools: true,
      vision: true,
      audio: false
    },
    {
      id: 'llama-roblox',
      name: 'Llama 3.1 8B Instruct',
      description: 'Meta开源的高效对话模型',
      category: 'meta',
      status: 'online',
      parameters: '8B',
      speed: 90,
      quality: 3,
      capabilities: ['对话', '工具调用'],
      isPremium: false,
      isNew: false,
      isFast: true,
      isFree: true,
      pricing: '免费',
      usageCount: 4560,
      contextLength: '4K',
      multimodal: false,
      provider: 'nebius',
      tier: 'anonymous',
      tools: true,
      vision: false,
      audio: false
    }
  ]
}

// 获取模型信息的工具函数
export async function getModelById(id) {
  const models = await getAIModels()
  return models.find(model => model.id === id)
}

export async function getModelsByCategory(category) {
  const models = await getAIModels()
  if (category === 'all') return models
  return models.filter(model => model.category === category)
}

export async function getOnlineModels() {
  const models = await getAIModels()
  return models.filter(model => model.status === 'online')
}

export async function getFreeModels() {
  const models = await getAIModels()
  return models.filter(model => model.isFree)
}

export async function getPremiumModels() {
  const models = await getAIModels()
  return models.filter(model => model.isPremium)
}

// 获取智谱模型列表
function getZhipuModels() {
  const zhipuModels = [
    {
      id: 'glm-4.5-air',
      name: 'GLM-4.5-Air',
      description: '智谱官方轻量级高效模型，响应速度快',
      category: 'zhipu',
      status: 'online',
      parameters: '9B',
      speed: 5,
      quality: 4,
      capabilities: ['chat', 'text', 'reasoning', 'analysis'],
      isPremium: false,
      isNew: true,
      isFast: true,
      isFree: true, // 已内置 API Key
      pricing: '免费',
      usageCount: Math.floor(Math.random() * 3000),
      contextLength: '8K',
      multimodal: false,
      provider: 'Zhipu AI',
      tools: true,
      vision: false,
      audio: false,
      reasoning: true,
      coding: false,
      keyRequired: false
    },
    {
      id: 'glm-4.5',
      name: 'GLM-4.5',
      description: '智谱官方标准版本模型，功能全面质量高',
      category: 'zhipu',
      status: 'online',
      parameters: '9B',
      speed: 4,
      quality: 5,
      capabilities: ['chat', 'text', 'reasoning', 'analysis', 'code'],
      isPremium: false,
      isNew: true,
      isFast: false,
      isFree: true, // 已内置 API Key
      pricing: '免费',
      usageCount: Math.floor(Math.random() * 5000),
      contextLength: '8K',
      multimodal: false,
      provider: 'Zhipu AI',
      tools: true,
      vision: false,
      audio: false,
      reasoning: true,
      coding: true,
      keyRequired: false
    }
  ]

  return zhipuModels
}

// 获取Claude模型列表
function getClaudeModels() {
  const claudeModels = [
    {
      id: 'claude-3.5-sonnet',
      name: 'Claude 3.5 Sonnet',
      description: 'Claude 3.5 Sonnet - 平衡性能与效率，适合日常对话',
      category: 'anthropic',
      status: 'online',
      parameters: '未知',
      speed: 4,
      quality: 5,
      capabilities: ['chat', 'text', 'reasoning', 'analysis', 'code'],
      isPremium: false,
      isNew: false,
      isFast: true,
      isFree: true, // 已内置 API Key
      pricing: '免费',
      usageCount: Math.floor(Math.random() * 8000),
      contextLength: '200K',
      multimodal: false,
      provider: 'Anthropic',
      tools: true,
      vision: false,
      audio: false,
      reasoning: true,
      coding: true,
      keyRequired: false
    },
    {
      id: 'claude-3.7-sonnet',
      name: 'Claude 3.7 Sonnet',
      description: 'Claude 3.7 Sonnet - 增强版本，更强的推理能力',
      category: 'anthropic',
      status: 'online',
      parameters: '未知',
      speed: 4,
      quality: 5,
      capabilities: ['chat', 'text', 'reasoning', 'analysis', 'code'],
      isPremium: false,
      isNew: true,
      isFast: true,
      isFree: true, // 已内置 API Key
      pricing: '免费',
      usageCount: Math.floor(Math.random() * 6000),
      contextLength: '200K',
      multimodal: false,
      provider: 'Anthropic',
      tools: true,
      vision: false,
      audio: false,
      reasoning: true,
      coding: true,
      keyRequired: false
    },
    {
      id: 'claude-4-sonnet',
      name: 'Claude 4 Sonnet',
      description: 'Claude 4 Sonnet - 最新最强版本，顶级性能',
      category: 'anthropic',
      status: 'online',
      parameters: '未知',
      speed: 3,
      quality: 5,
      capabilities: ['chat', 'text', 'reasoning', 'analysis', 'code'],
      isPremium: false,
      isNew: true,
      isFast: false,
      isFree: true, // 已内置 API Key
      pricing: '免费',
      usageCount: Math.floor(Math.random() * 4000),
      contextLength: '200K',
      multimodal: false,
      provider: 'Anthropic',
      tools: true,
      vision: false,
      audio: false,
      reasoning: true,
      coding: true,
      keyRequired: false
    },
    {
      id: 'deepseek-r1',
      name: 'DeepSeek R1',
      description: 'DeepSeek R1 - 推理优化模型，专注于逻辑推理',
      category: 'deepseek',
      status: 'online',
      parameters: '未知',
      speed: 4,
      quality: 5,
      capabilities: ['chat', 'text', 'reasoning', 'analysis', 'code'],
      isPremium: false,
      isNew: true,
      isFast: true,
      isFree: false, // 需要 API Key
      pricing: '第三方 API',
      usageCount: Math.floor(Math.random() * 5000),
      contextLength: '128K',
      multimodal: false,
      provider: 'DeepSeek',
      tools: true,
      vision: false,
      audio: false,
      reasoning: true,
      coding: true,
      keyRequired: true
    },
    {
      id: 'openai-gpt-4.1',
      name: 'OpenAI GPT-4.1',
      description: 'OpenAI GPT-4.1 - 最新 GPT 模型，全面能力提升',
      category: 'openai',
      status: 'online',
      parameters: '未知',
      speed: 4,
      quality: 5,
      capabilities: ['chat', 'text', 'reasoning', 'analysis', 'code'],
      isPremium: true,
      isNew: true,
      isFast: true,
      isFree: false, // 需要 API Key
      pricing: '第三方 API',
      usageCount: Math.floor(Math.random() * 7000),
      contextLength: '128K',
      multimodal: false,
      provider: 'OpenAI',
      tools: true,
      vision: false,
      audio: false,
      reasoning: true,
      coding: true,
      keyRequired: true
    }
  ]

  return claudeModels
}

// 获取Doubao模型列表
function getDoubaoModels() {
  const doubaoModels = [
    {
      id: 'doubao-pro-32k',
      name: 'Doubao Pro 32K',
      description: '豆包AI专业版，支持32K上下文，可进行对话和生成图片',
      category: 'doubao',
      status: 'online',
      parameters: '未知',
      speed: 4,
      quality: 5,
      capabilities: ['chat', 'text', 'image-generation', 'multimodal'],
      isPremium: false,
      isNew: true,
      isFast: true,
      isFree: true,
      pricing: '免费',
      usageCount: Math.floor(Math.random() * 5000),
      contextLength: '32K',
      multimodal: true,
      provider: 'Doubao AI',
      tools: false,
      vision: false,
      audio: false,
      reasoning: true,
      coding: true,
      imageGeneration: true,
      keyRequired: false
    }
  ]

  return doubaoModels
}

// 获取Qwen-image模型列表
function getQwenImageModels() {
  const qwenImageModels = [
    {
      id: 'qwen-plus',
      name: 'Qwen Plus',
      description: 'Qwen Plus - 阿里云通义千问图像生成模型，支持推理过程展示',
      category: 'qwen',
      status: 'online',
      parameters: '未知',
      speed: 4,
      quality: 5,
      capabilities: ['chat', 'text', 'image-generation', 'reasoning'],
      isPremium: false,
      isNew: true,
      isFast: true,
      isFree: true,
      pricing: '免费',
      usageCount: Math.floor(Math.random() * 3000),
      contextLength: '8K',
      multimodal: true,
      provider: 'Alibaba Cloud',
      tools: false,
      vision: false,
      audio: false,
      reasoning: true,
      coding: false,
      imageGeneration: true,
      keyRequired: false
    }
  ]

  return qwenImageModels
}

export async function searchModels(query) {
  const models = await getAIModels()
  const lowerQuery = query.toLowerCase()
  return models.filter(model =>
    model.name.toLowerCase().includes(lowerQuery) ||
    model.description.toLowerCase().includes(lowerQuery) ||
    model.capabilities.some(cap => cap.toLowerCase().includes(lowerQuery))
  )
}