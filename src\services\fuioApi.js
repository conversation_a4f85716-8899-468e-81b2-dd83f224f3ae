/**
 * Fuio.tech API 服务
 * 支持文字生图和图文生图功能
 */

// API配置
const FUIO_CONFIG = {
  baseUrl: 'https://api.fuio.tech',
  apiKey: 'sk-NE03sGi4hBFpbUwgidmBPNZonT3SjvaAQ3eG45s4idAHWa5i',
  model: 'sora-img', // 原始模型名称
  // 备用模型名称，如果 sora-img 不可用可以尝试这些
  alternativeModels: ['gpt-image-1', 'dall-e-3', 'stable-diffusion', 'midjourney'],
  timeout: 180000, // 将默认超时提升至180s以适配慢速图像生成
  maxRetries: 3,   // 最大重试次数
  retryDelays: [1000, 2000, 4000] // 指数退避：1s/2s/4s
}

// 图片尺寸配置
const IMAGE_SIZES = {
  '1:1': { width: 480, height: 480 },
  '2:3': { width: 480, height: 720 },
  '3:2': { width: 720, height: 480 }
}

/**
 * 将图片文件转换为base64
 */
async function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      // 移除data:image/...;base64,前缀
      const base64 = reader.result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

/**
 * 发送请求到Fuio API，支持模型回退机制
 */
async function makeRequest(endpoint, data, retryWithAlternativeModel = true) {
  const url = `${FUIO_CONFIG.baseUrl}${endpoint}`

  let lastError = null

  // 支持最多 maxRetries 次重试（不含首次）
  for (let attempt = 0; attempt <= (FUIO_CONFIG.maxRetries || 0); attempt++) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), FUIO_CONFIG.timeout)

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${FUIO_CONFIG.apiKey}`
        },
        body: JSON.stringify(data),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text().catch(() => '')
        let errorData = {}
        try { errorData = JSON.parse(errorText) } catch {}
        const errorMessage = errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`

        // SSL/网络类错误在非OK也可能没有，这里主要用于200范围外
        // 模型资源不可用 => 备用模型回退
        if (
          retryWithAlternativeModel &&
          (errorMessage.includes('无可用资源') ||
           errorMessage.includes('model not available') ||
           errorMessage.includes('gpt-image-1'))
        ) {
          console.warn('⚠️ 当前模型不可用，尝试使用备用模型:', errorMessage)
          for (const altModel of FUIO_CONFIG.alternativeModels) {
            try {
              console.log('🔄 尝试备用模型:', altModel)
              const altData = { ...data, model: altModel }
              return await makeRequest(endpoint, altData, false)
            } catch (altError) {
              console.warn(`❌ 备用模型 ${altModel} 失败:`, altError.message)
            }
          }
        }

        // 可重试的服务端错误
        if ([429, 500, 502, 503, 504].includes(response.status) && attempt < FUIO_CONFIG.maxRetries) {
          lastError = new Error(errorMessage)
          const delay = (FUIO_CONFIG.retryDelays?.[attempt]) ?? 1000 * (attempt + 1)
          await new Promise(r => setTimeout(r, delay))
          continue
        }

        throw new Error(errorMessage)
      }

      // 处理流式响应（用于 /v1/chat/completions 端点）
      if (endpoint === '/v1/chat/completions' && data.stream) {
        return await handleStreamResponse(response)
      }

      return await response.json()
    } catch (error) {
      clearTimeout(timeoutId)

      // 处理超时/中断
      if (error.name === 'AbortError') {
        lastError = new Error('请求超时，请稍后重试')
        if (attempt < FUIO_CONFIG.maxRetries) {
          const delay = (FUIO_CONFIG.retryDelays?.[attempt]) ?? 1000 * (attempt + 1)
          await new Promise(r => setTimeout(r, delay))
          continue
        }
        throw lastError
      }

      // 处理网络/SSL异常：ERR_SSL_PROTOCOL_ERROR / Failed to fetch 等
      const msg = String(error.message || '')
      const isNetworkLike =
        msg.includes('Failed to fetch') ||
        msg.includes('NetworkError') ||
        msg.includes('ERR_SSL_PROTOCOL_ERROR')

      if (isNetworkLike && attempt < FUIO_CONFIG.maxRetries) {
        lastError = new Error('网络不稳定，正在重试...')
        const delay = (FUIO_CONFIG.retryDelays?.[attempt]) ?? 1000 * (attempt + 1)
        await new Promise(r => setTimeout(r, delay))
        continue
      }

      throw error
    }
  }

  // 理论不达
  throw lastError || new Error('请求失败')
}

/**
 * 处理流式响应
 */
async function handleStreamResponse(response) {
  const reader = response.body.getReader()
  const decoder = new TextDecoder()
  let buffer = ''
  let result = null

  try {
    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      buffer += decoder.decode(value, { stream: true })
      const lines = buffer.split('\n')
      buffer = lines.pop() || '' // 保留最后一行（可能不完整）

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6).trim()
          if (data === '[DONE]') {
            break
          }

          try {
            const parsed = JSON.parse(data)
            if (parsed.choices && parsed.choices.length > 0) {
              result = parsed
              // 对于图像生成，我们通常只需要最后一个完整的响应
              if (parsed.choices[0].finish_reason) {
                break
              }
            }
          } catch (e) {
            // 忽略解析错误，继续处理下一行
            continue
          }
        }
      }
    }

    return result || { choices: [{ message: { content: '' } }] }
  } finally {
    reader.releaseLock()
  }
}

/**
 * 文字生图
 * @param {string} prompt - 文字描述
 * @param {Object} options - 生成选项
 */
export async function generateTextToImage(prompt, options = {}) {
  try {
    if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
      throw new Error('请提供有效的图像描述')
    }

    // 解析尺寸
    const aspectRatio = options.aspectRatio || '1:1'
    const size = IMAGE_SIZES[aspectRatio] || IMAGE_SIZES['1:1']

    const requestData = {
      model: FUIO_CONFIG.model,
      prompt: prompt.trim(),
      n: options.n || 1,
      size: `${size.width}x${size.height}`,
      response_format: 'url'
    }

    console.log('🎨 发送文字生图请求:', requestData)

    const response = await makeRequest('/v1/images/generations', requestData)

    if (response.data && response.data.length > 0) {
      const images = response.data.map((item, index) => ({
        url: item.url,
        index: index
      }))

      return {
        success: true,
        images: images,
        prompt: prompt,
        model: FUIO_CONFIG.model,
        size: `${size.width}x${size.height}`,
        count: images.length
      }
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('❌ 文字生图失败:', error)
    return {
      success: false,
      error: error.message || '图像生成失败'
    }
  }
}

/**
 * 图文生图（图像编辑）
 * @param {string} prompt - 文字描述
 * @param {File|string} image - 图片文件或base64字符串
 * @param {Object} options - 生成选项
 */
export async function generateImageToImage(prompt, image, options = {}) {
  try {
    if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
      throw new Error('请提供有效的图像描述')
    }

    if (!image) {
      throw new Error('请提供参考图像')
    }

    // 处理图像数据 - 转换为data URL格式
    let imageDataUrl
    if (typeof image === 'string') {
      // 如果是base64字符串，确保有正确的前缀
      if (image.startsWith('data:')) {
        imageDataUrl = image
      } else {
        imageDataUrl = `data:image/png;base64,${image}`
      }
    } else if (image instanceof File) {
      // 如果是文件，转换为完整的data URL
      imageDataUrl = await new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => resolve(reader.result)
        reader.onerror = reject
        reader.readAsDataURL(image)
      })
    } else {
      throw new Error('不支持的图像格式')
    }

    // 构建消息格式的请求数据（根据成功案例）
    const requestData = {
      model: FUIO_CONFIG.model,
      temperature: 0.6,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt.trim()
            },
            {
              type: "image_url",
              image_url: {
                url: imageDataUrl
              }
            }
          ]
        }
      ],
      stream: true,
      stream_options: {
        include_usage: true
      }
    }

    console.log('🎨 发送图文生图请求:', {
      ...requestData,
      messages: [
        {
          ...requestData.messages[0],
          content: [
            requestData.messages[0].content[0],
            {
              type: "image_url",
              image_url: {
                url: `[image data: ${imageDataUrl.length} chars]`
              }
            }
          ]
        }
      ]
    })

    console.log('📋 使用的模型名称:', FUIO_CONFIG.model)

    const response = await makeRequest('/v1/chat/completions', requestData)

    // 处理流式响应（根据成功案例的响应格式）
    if (response.choices && response.choices.length > 0) {
      const choice = response.choices[0]
      let content = ''

      // 如果是流式响应，需要处理消息内容
      if (choice.message && choice.message.content) {
        content = choice.message.content
      } else if (choice.delta && choice.delta.content) {
        content = choice.delta.content
      }

      // 尝试从响应中提取图像URL
      // 根据成功案例，图像可能在content中或者作为单独的字段返回
      let imageUrl = null

      // 检查是否有图像URL在响应中
      if (content.includes('http')) {
        const urlMatch = content.match(/https?:\/\/[^\s]+/g)
        if (urlMatch && urlMatch.length > 0) {
          imageUrl = urlMatch[0]
        }
      }

      // 如果没有找到图像URL，可能需要从其他字段获取
      if (!imageUrl && response.data && response.data.length > 0) {
        imageUrl = response.data[0].url
      }

      if (imageUrl) {
        return {
          success: true,
          images: [{
            url: imageUrl,
            index: 0
          }],
          prompt: prompt,
          model: FUIO_CONFIG.model,
          count: 1,
          type: 'image-to-image',
          content: content
        }
      } else {
        throw new Error('未能从响应中获取图像URL')
      }
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('❌ 图文生图失败:', error)

    // 提供更友好的错误信息（不中断UI，提示可恢复）
    const msg = error.message || ''
    let friendlyError = '图像编辑失败，请稍后重试'
    if (msg.includes('无可用资源')) {
      friendlyError = '当前模型资源不足，已自动尝试回退，请稍后重试'
    } else if (msg.includes('gpt-image-1')) {
      friendlyError = '模型配置问题，已尝试使用备用模型'
    } else if (msg.includes('timeout') || error.name === 'AbortError') {
      friendlyError = '处理时间较长导致超时，已尝试重试；请稍后再试或缩短描述'
    } else if (msg.includes('ERR_SSL_PROTOCOL_ERROR') || msg.includes('Failed to fetch')) {
      friendlyError = '网络/SSL异常，已自动重试；如仍失败请检查网络或稍后重试'
    }

    return {
      success: false,
      error: friendlyError,
      originalError: msg,
      model: FUIO_CONFIG.model,
      recoverable: true // 提示前端可保持loading/不中断
    }
  }
}

/**
 * 获取支持的图片尺寸
 */
export function getSupportedSizes() {
  return Object.keys(IMAGE_SIZES).map(ratio => ({
    ratio,
    ...IMAGE_SIZES[ratio],
    label: `${ratio} (${IMAGE_SIZES[ratio].width}×${IMAGE_SIZES[ratio].height})`
  }))
}

/**
 * 验证API密钥
 */
export async function validateApiKey() {
  try {
    // 发送一个简单的请求来验证API密钥
    const response = await makeRequest('/v1/models', {})
    return {
      success: true,
      models: response.data || []
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

export default {
  generateTextToImage,
  generateImageToImage,
  getSupportedSizes,
  validateApiKey,
  config: FUIO_CONFIG
}
