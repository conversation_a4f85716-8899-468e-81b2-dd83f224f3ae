<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doubao API 修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .loading {
            border-left-color: #ffc107;
            background-color: #fff3cd;
            color: #856404;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Doubao API 修复测试</h1>
        
        <div class="test-section">
            <h3>📡 API 连接测试</h3>
            <p>测试修复后的 Doubao API 服务连接</p>
            <button onclick="testConnection()">测试连接</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💬 正常对话测试</h3>
            <p>测试正常格式的消息</p>
            <input type="text" id="normalPrompt" placeholder="输入正常对话内容" value="你好，请介绍一下自己">
            <button onclick="testNormalChat()">发送正常消息</button>
            <div id="normalResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔧 异常消息格式测试</h3>
            <p>测试修复异常格式的消息（模拟之前的错误情况）</p>
            <button onclick="testBrokenMessage()">测试异常消息格式</button>
            <div id="brokenResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🎨 图像生成测试</h3>
            <p>测试 Doubao 图像生成功能</p>
            <input type="text" id="imagePrompt" placeholder="输入图像描述" value="画一只可爱的小猫">
            <button onclick="testImageGeneration()">生成图像</button>
            <div id="imageResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 直接实现API调用
        const API_BASE_URL = 'http://*************:8000';
        const API_KEY = 'doubao_user_388b1a84';

        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试连接...';

            try {
                const response = await fetch(API_BASE_URL + '/', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ Doubao API 服务连接成功！\n状态码: ' + response.status;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Doubao API 服务连接失败\n状态码: ' + response.status;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 连接测试失败:\n' + error.message;
            }
        }

        async function testNormalChat() {
            const prompt = document.getElementById('normalPrompt').value;
            const resultDiv = document.getElementById('normalResult');
            
            if (!prompt.trim()) {
                alert('请输入对话内容');
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在发送正常格式消息...';

            try {
                const requestBody = {
                    model: 'doubao-pro-32k',
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    temperature: 0.7,
                    stream: false
                };

                console.log('发送正常消息请求:', requestBody);

                const response = await fetch(API_BASE_URL + '/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('正常消息响应状态:', response.status);
                const responseText = await response.text();
                console.log('正常消息响应内容:', responseText);

                if (response.ok) {
                    const result = JSON.parse(responseText);
                    const content = result.choices?.[0]?.message?.content || '无回复内容';
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 正常消息发送成功！\n\n回复内容:\n' + content + '\n\n完整响应:\n' + JSON.stringify(result, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 正常消息发送失败:\n状态码: ' + response.status + '\n响应: ' + responseText;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 正常消息测试失败:\n' + error.message;
                console.error('正常消息错误详情:', error);
            }
        }

        async function testBrokenMessage() {
            const resultDiv = document.getElementById('brokenResult');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试异常消息格式（模拟之前的错误）...';

            try {
                // 模拟之前导致错误的消息格式
                const requestBody = {
                    model: 'doubao-pro-32k',
                    messages: [
                        { content: '已切换到 Doubao Pro 32K' }, // 缺少 role 字段
                        { role: 'user', content: '你好' }
                    ],
                    temperature: 0.7,
                    stream: false
                };

                console.log('发送异常格式消息请求:', requestBody);

                const response = await fetch(API_BASE_URL + '/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('异常消息响应状态:', response.status);
                const responseText = await response.text();
                console.log('异常消息响应内容:', responseText);

                if (response.ok) {
                    const result = JSON.parse(responseText);
                    const content = result.choices?.[0]?.message?.content || '无回复内容';
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 异常消息格式测试成功！\n说明：服务器端已修复消息格式问题\n\n回复内容:\n' + content;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 异常消息格式仍然失败:\n状态码: ' + response.status + '\n响应: ' + responseText + '\n\n这表明需要在客户端修复消息格式';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 异常消息格式测试失败:\n' + error.message;
                console.error('异常消息错误详情:', error);
            }
        }

        async function testImageGeneration() {
            const prompt = document.getElementById('imagePrompt').value;
            const resultDiv = document.getElementById('imageResult');
            
            if (!prompt.trim()) {
                alert('请输入图像描述');
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在生成图像，请稍候...';

            try {
                const requestBody = {
                    model: 'doubao-pro-32k',
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    temperature: 0.7,
                    stream: false
                };

                console.log('发送图像生成请求:', requestBody);

                const response = await fetch(API_BASE_URL + '/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('图像生成响应状态:', response.status);
                const responseText = await response.text();
                console.log('图像生成响应内容:', responseText);

                if (response.ok) {
                    const result = JSON.parse(responseText);
                    const content = result.choices?.[0]?.message?.content || '无回复内容';
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 图像生成请求成功！\n\n回复内容:\n' + content + '\n\n完整响应:\n' + JSON.stringify(result, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 图像生成失败:\n状态码: ' + response.status + '\n响应: ' + responseText;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 图像生成测试失败:\n' + error.message;
                console.error('图像生成错误详情:', error);
            }
        }
    </script>
</body>
</html>