<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 chat-mobile-optimized">
    <!-- 移动端侧边栏遮罩层 -->
    <div
      class="sidebar-overlay"
      :class="{ active: !sidebarCollapsed }"
      @click="toggleSidebar"
    ></div>

    <!-- 主布局 -->
    <div class="flex h-screen chat-container">
      <!-- 侧边栏 -->
      <aside
        class="chat-sidebar bg-white border-r border-gray-200 transition-all duration-300 ease-in-out shadow-soft"
        :class="[
          sidebarCollapsed ? 'w-16' : 'w-80',
          { 'visible': !sidebarCollapsed }
        ]"
      >
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-between p-4 border-b border-gray-100">
          <button
            @click="toggleSidebar"
            class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-600 hover:text-gray-900"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="transition-transform duration-200" :class="{ 'rotate-180': sidebarCollapsed }">
              <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
          <h1
            v-if="!sidebarCollapsed"
            class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent animate-fade-in"
          >
            AI 智能对话
          </h1>
        </div>

        <!-- 展开状态的侧边栏内容 -->
        <div v-if="!sidebarCollapsed" class="flex flex-col h-full">
          <!-- 模型选择器 -->
          <div class="p-4 border-b border-gray-100">
            <div class="flex items-center justify-between mb-2">
              <label class="block text-sm font-medium text-gray-700">选择AI模型</label>
              <button
                @click="showApiConfig = true"
                class="text-xs text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                title="配置API密钥"
              >
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" stroke="currentColor" stroke-width="2"/>
                  <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>配置</span>
              </button>
            </div>

            <!-- 使用新的模型选择器组件 -->
            <ModelSelector
              v-model="selectedModel"
              @model-changed="handleModelChange"
            />

            <!-- 显示当前选中模型的详细信息 -->
            <div v-if="currentModelInfo" class="mt-3 p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-2 mb-2">
                <div class="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                  <span class="text-xs text-white font-bold">{{ currentModelInfo.name.charAt(0) }}</span>
                </div>
                <span class="text-sm font-medium text-gray-900">{{ currentModelInfo.name }}</span>
                <span v-if="currentModelInfo.isFree" class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">免费</span>
              </div>
              <p class="text-xs text-gray-600 mb-2">{{ currentModelInfo.description }}</p>
              <div class="flex items-center space-x-4 text-xs text-gray-500">
                <span>速度: {{ currentModelInfo.speed || 'N/A' }}/100</span>
                <span>质量: {{ currentModelInfo.quality || 'N/A' }}/5</span>
              </div>
            </div>
          </div>

          <!-- 对话列表 -->
          <div class="flex-1 overflow-y-auto p-4 space-y-2">
            <div class="text-sm font-medium text-gray-500 mb-3">对话历史</div>
            <button
              v-for="conversation in chatStore.conversations"
              :key="conversation.id"
              @click="switchConversation(conversation.id)"
              class="w-full text-left p-3 rounded-lg transition-all duration-200 group"
              :class="conversation.id === chatStore.currentConversationId
                ? 'bg-blue-50 border border-blue-200 text-blue-900 shadow-sm'
                : 'hover:bg-gray-50 text-gray-700 hover:text-gray-900'"
            >
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium truncate">{{ conversation.title || '新对话' }}</span>
                <svg
                  v-if="conversation.id === chatStore.currentConversationId"
                  width="16" height="16" viewBox="0 0 24 24" fill="none" class="text-blue-500 flex-shrink-0"
                >
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="text-xs text-gray-500 mt-1">
                {{ formatTime(conversation.updatedAt) }}
              </div>
            </button>
          </div>

          <!-- 新建对话按钮 -->
          <div class="p-4 border-t border-gray-100">
            <button
              @click="createNewConversation"
              class="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-medium hover:shadow-strong transform hover:-translate-y-0.5"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              <span class="font-medium">新建对话</span>
            </button>
          </div>
        </div>

        <!-- 折叠状态的侧边栏内容 -->
        <div v-else class="flex flex-col items-center py-4 space-y-4">
          <button
            @click="createNewConversation"
            class="p-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-medium hover:shadow-strong transform hover:-translate-y-0.5"
            title="新建对话"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="chat-main flex-1 flex flex-col bg-white">
        <!-- 聊天头部 -->
        <header class="chat-header flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-white/80 backdrop-blur-sm">
          <div class="header-content flex items-center space-x-3">
            <!-- 移动端汉堡菜单按钮 -->
            <button
              @click="toggleSidebar"
              class="mobile-menu-btn p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200 md:hidden"
              title="菜单"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>

            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse-gentle"></div>
            <h2 class="chat-title text-lg font-semibold text-gray-900">
              {{ chatStore.currentConversation?.title || '新对话' }}
            </h2>
            <span class="model-badge px-2 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-full">
              {{ selectedModel }}
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <button
              class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="清空对话"
              @click="clearConversation"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button
              class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="设置"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
        </header>

        <!-- 消息区域 -->
        <section class="flex-1 overflow-hidden">
          <MessageList
            ref="messageListRef"
            :messages="chatStore.value?.currentMessages || []"
            :has-messages="chatStore.value?.hasMessages || false"
            :loading="chatStore.value?.loading || false"
            :suggested-questions="suggestedQuestions"
            :should-auto-scroll="shouldAutoScroll"
            @send-message="sendMessage"
            @copy-message="copyMessage"
            @regenerate-message="regenerateMessage"
            @load-more="loadMoreMessages"
            @scroll-change="handleScrollChange"
          />
        </section>

        <!-- 输入区域 -->
        <footer class="border-t border-gray-200 bg-white/80 backdrop-blur-sm">
          <ChatInput
            ref="chatInputRef"
            :loading="chatStore.value?.loading || false"
            :disabled="false"
            @send-message="sendMessage"
            @start-voice-input="startVoiceInput"
            @file-upload="handleFileUpload"
          />
        </footer>
      </main>
    </div>

    <!-- API配置对话框 -->
    <el-dialog
      v-model="showApiConfig"
      title="API密钥配置"
      width="80%"
      :close-on-click-modal="false"
      class="api-config-dialog"
    >
      <ApiKeyConfig @provider-configured="handleProviderConfigured" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { copyToClipboard } from '@/utils/common'
import { marked } from 'marked'
import hljs from 'highlight.js'

// 导入聊天组件
import ChatSidebar from '@/components/chat/ChatSidebar.vue'
import ChatHeader from '@/components/chat/ChatHeader.vue'
import MessageList from '@/components/chat/MessageList.vue'
import ChatInput from '@/components/chat/ChatInput.vue'
import ModelSelector from '@/components/chat/ModelSelector.vue'
import ApiKeyConfig from '@/components/ApiKeyConfig.vue'

// 导入AI模型配置
import { getAIModels, getModelById } from '@/config/aiModels.js'

// 使用响应式引用来安全地初始化 stores
const chatStore = ref(null)
const userStore = ref(null)

// 响应式数据
const sidebarCollapsed = ref(false)
const showSettings = ref(false)
const showApiConfig = ref(false)
const selectedModel = ref('gemini-2.5-flash')
const voiceModeEnabled = ref(false)
const shouldAutoScroll = ref(true)
const aiModels = ref([])
const currentModelInfo = ref(null)

// 组件引用
const messageListRef = ref(null)
const chatInputRef = ref(null)

// 建议问题
const suggestedQuestions = [
  '帮我写一个创意故事',
  '解释一下人工智能的原理',
  '推荐一些学习编程的方法',
  '如何提高工作效率？',
  '分析这个问题的解决方案',
  '帮我制定学习计划',
]

// 计算属性
const currentConversationTitle = computed(() => {
  return chatStore.currentConversation?.title || '新对话'
})

// 方法
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

  return date.toLocaleDateString()
}

const handleScrollChange = (scrollInfo) => {
  shouldAutoScroll.value = scrollInfo.isNearBottom
}

const loadMoreMessages = async () => {
  if (chatStore.loading || !chatStore.hasMoreMessages) return
  await chatStore.loadMoreMessages()
}

// 加载AI模型
const loadAIModels = async () => {
  try {
    aiModels.value = await getAIModels()
    await updateCurrentModelInfo()
  } catch (error) {
    console.error('加载AI模型失败:', error)
    ElMessage.error('加载AI模型失败')
  }
}

// 更新当前模型信息
const updateCurrentModelInfo = async () => {
  try {
    currentModelInfo.value = await getModelById(selectedModel.value)
  } catch (error) {
    console.error('获取模型信息失败:', error)
  }
}

// 处理模型变更
const handleModelChange = async (modelId) => {
  selectedModel.value = modelId
  await updateCurrentModelInfo()
  ElMessage.success(`已切换到 ${currentModelInfo.value?.name || modelId}`)
}

const clearConversation = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空当前对话吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    await chatStore.clearCurrentConversation()
    ElMessage.success('对话已清空')
  } catch (error) {
    // 用户取消操作
  }
}

const handleFileUpload = (files) => {
  console.log('文件上传:', files)
}

const startVoiceInput = (isRecording) => {
  console.log('语音输入:', isRecording)
}

const sendMessage = async (content) => {
  try {
    await chatStore.value.sendMessage(content)
    ElMessage.success('消息发送成功')
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败，请重试')
  }
}

const regenerateMessage = async (messageId) => {
  try {
    await chatStore.value.regenerateMessage(messageId)
    ElMessage.success('正在重新生成回答...')
  } catch (error) {
    console.error('重新生成失败:', error)
    ElMessage.error('重新生成失败，请重试')
  }
}

const copyMessage = async (content) => {
  try {
    await copyToClipboard(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const createNewConversation = async () => {
  try {
    chatStore.value.createConversation()
    ElMessage.success('已创建新对话')
  } catch (error) {
    console.error('创建对话失败:', error)
    ElMessage.error('创建对话失败，请重试')
  }
}

const switchConversation = async (conversationId) => {
  try {
    chatStore.value.switchConversation(conversationId)
  } catch (error) {
    console.error('切换对话失败:', error)
    ElMessage.error('切换对话失败，请重试')
  }
}

const handleConversationCommand = async (command, conversationId) => {
  try {
    switch (command) {
      case 'delete':
        await ElMessageBox.confirm('确定要删除这个对话吗？', '确认删除', {
          type: 'warning'
        })
        await chatStore.deleteConversation(conversationId)
        ElMessage.success('对话已删除')
        break
      case 'rename':
        const { value: newTitle } = await ElMessageBox.prompt('请输入新的对话标题', '重命名对话')
        if (newTitle) {
          await chatStore.renameConversation(conversationId, newTitle)
          ElMessage.success('对话已重命名')
        }
        break
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error('操作失败，请重试')
    }
  }
}

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const toggleVoiceMode = () => {
  voiceModeEnabled.value = !voiceModeEnabled.value
  ElMessage.info(voiceModeEnabled.value ? '语音模式已开启' : '语音模式已关闭')
}

// 处理API配置完成
const handleProviderConfigured = (provider) => {
  ElMessage.success(`${provider.name} 配置完成`)
  showApiConfig.value = false
  // 重新加载模型列表
  loadAIModels()
}

const exportChat = async () => {
  try {
    const messages = chatStore.currentMessages
    const content = messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n')
    
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `chat-${Date.now()}.txt`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('聊天记录已导出')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const clearCurrentChat = async () => {
  try {
    await ElMessageBox.confirm('确定要清空当前对话吗？', '确认清空', {
      type: 'warning'
    })
    await chatStore.clearCurrentConversation()
    ElMessage.success('对话已清空')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空失败:', error)
      ElMessage.error('清空失败，请重试')
    }
  }
}

// 生命周期
onMounted(async () => {
  try {
    // 动态导入 stores
    const { useChatStore } = await import('@/stores/chat')
    const { useUserStore } = await import('@/stores/user')

    chatStore.value = useChatStore()
    userStore.value = useUserStore()

    await chatStore.value.initializeChat()

    // 加载AI模型
    await loadAIModels()

    marked.setOptions({
      highlight: function(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(code, { language: lang }).value
          } catch (err) {
            console.error('代码高亮失败:', err)
          }
        }
        return hljs.highlightAuto(code).value
      },
      breaks: true,
      gfm: true
    })
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  }
})

onUnmounted(() => {
  // 清理资源
})

// 监听消息变化
watch(() => chatStore.currentMessages, () => {
  // 消息变化时的处理
}, { deep: true })
</script>

<style lang="scss" scoped>
/* 移动端适配样式 */
@media (max-width: 768px) {
  .chat-mobile-optimized {
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
    position: relative;
  }

  .chat-container {
    height: 100vh;
    height: 100dvh;
    position: relative;
    overflow: hidden;
  }

  /* 侧边栏移动端适配 */
  .chat-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    height: 100dvh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 90vw !important;
    max-width: 350px !important;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);

    /* 当侧边栏展开时 */
    &:not(.w-16) {
      transform: translateX(0);
    }

    /* 侧边栏头部优化 */
    .flex.items-center.justify-between.p-4 {
      padding: 1rem;
      border-bottom: 1px solid #f1f5f9;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

      h1 {
        font-size: 1.125rem;
        font-weight: 700;
      }

      button {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(0, 0, 0, 0.05);
        
        &:hover {
          background: white;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }

    /* 模型选择区域优化 */
    .p-4.border-b {
      padding: 1rem;
      background: white;

      label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.75rem;
      }

      /* 模型选择器触发器优化 */
      .model-selector .selector-trigger {
        padding: 0.875rem;
        border-radius: 10px;
        border: 2px solid #e5e7eb;
        background: #f9fafb;
        
        &:hover {
          border-color: #3b82f6;
          background: white;
        }

        .model-info {
          .model-name {
            font-size: 0.9rem;
            font-weight: 600;
          }

          .model-desc {
            font-size: 0.8rem;
            color: #6b7280;
          }
        }
      }

      /* 当前模型信息卡片 */
      .mt-3.p-3 {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 1px solid #bae6fd;
        border-radius: 10px;
        padding: 0.875rem;

        .flex.items-center.space-x-2 {
          margin-bottom: 0.5rem;

          .w-6.h-6 {
            width: 1.5rem;
            height: 1.5rem;
          }

          span {
            font-size: 0.875rem;
            font-weight: 600;
          }
        }

        p {
          font-size: 0.8rem;
          line-height: 1.4;
          margin-bottom: 0.5rem;
        }

        .flex.items-center.space-x-4 {
          font-size: 0.75rem;
          color: #6b7280;
        }
      }
    }

    /* 对话历史区域优化 */
    .flex-1.overflow-y-auto.p-4 {
      padding: 1rem;
      background: #fafafa;

      .text-sm.font-medium {
        font-size: 0.8rem;
        font-weight: 600;
        color: #6b7280;
        margin-bottom: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.025em;
      }

      /* 对话历史按钮 */
      button {
        padding: 0.875rem;
        border-radius: 10px;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;

        &:hover {
          transform: translateX(2px);
        }

        .flex.items-center.justify-between {
          .text-sm {
            font-size: 0.875rem;
            font-weight: 500;
          }

          .text-xs {
            font-size: 0.75rem;
          }
        }
      }
    }

    /* 新建对话按钮区域 */
    .p-4.border-t {
      padding: 1rem;
      background: white;
      border-top: 1px solid #f1f5f9;

      button {
        width: 100%;
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
        font-weight: 600;
        border-radius: 10px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        
        &:hover {
          background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .gap-2 {
          gap: 0.5rem;
        }

        svg {
          width: 18px;
          height: 18px;
        }
      }
    }
  }

  /* 移动端汉堡菜单按钮 */
  .mobile-menu-btn {
    display: flex !important;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    margin-right: 0.75rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.05);
    width: 40px;
    height: 40px;
    
    &:hover {
      background: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: scale(1.05);
    }

    svg {
      width: 20px;
      height: 20px;
    }
  }

  /* 主内容区域 */
  .chat-main {
    width: 100vw;
    margin-left: 0;
    padding: 0;
    position: relative;
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
  }

  /* 聊天头部移动端优化 */
  .chat-header {
    padding: 0.875rem 1rem;
    min-height: 64px;
    border-bottom: 1px solid #e5e7eb;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    flex-shrink: 0;

    .header-content {
      gap: 0.75rem;
      align-items: center;

      .w-3.h-3 {
        width: 0.75rem;
        height: 0.75rem;
        flex-shrink: 0;
      }

      .chat-title {
        font-size: 1.125rem;
        font-weight: 700;
        color: #1f2937;
        line-height: 1.2;
      }

      .model-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.625rem;
        border-radius: 12px;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1d4ed8;
        border: 1px solid #93c5fd;
        font-weight: 600;
        flex-shrink: 0;
      }
    }

    .flex.items-center.space-x-2 {
      gap: 0.5rem;

      button {
        width: 40px;
        height: 40px;
        padding: 0.5rem;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
        
        &:hover {
          background: white;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transform: scale(1.05);
        }
        
        svg {
          width: 18px;
          height: 18px;
        }
      }
    }
  }

  /* 消息区域优化 */
  .flex-1.overflow-hidden {
    flex: 1;
    height: auto;
    min-height: 0;
  }

  /* 输入区域优化 */
  footer {
    padding: 1rem;
    min-height: auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid #e5e7eb;
    flex-shrink: 0;
    
    // iOS安全区域适配
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
  }
}

@media (max-width: 480px) {
  /* 侧边栏超小屏幕优化 */
  aside {
    width: 95vw !important;
    max-width: 300px !important;
  }

  /* 移动端汉堡菜单按钮优化 */
  .mobile-menu-btn {
    padding: 0.375rem;
    width: 36px;
    height: 36px;
    margin-right: 0.375rem;
  }

  /* 聊天头部超小屏幕优化 */
  .chat-header {
    padding: 0.5rem 0.75rem;

    .header-content {
      gap: 0.375rem;

      .chat-title {
        font-size: 0.9rem;
      }

      .model-badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
      }

      .w-3.h-3 {
        width: 0.625rem;
        height: 0.625rem;
      }
    }

    .flex.items-center.space-x-2 {
      gap: 0.125rem;

      button {
        width: 32px;
        height: 32px;
        padding: 0.375rem;
      }
    }
  }

  /* 侧边栏内容优化 */
  aside {
    .p-4 {
      padding: 0.75rem;
    }

    .text-xl {
      font-size: 1.125rem;
    }

    .text-lg {
      font-size: 1rem;
    }
  }

  /* 输入区域更紧凑 */
  footer {
    padding: 0.5rem 0.75rem;
    min-height: 70px;
    padding-bottom: calc(0.5rem + env(safe-area-inset-bottom));
  }

  /* 消息区域高度调整 */
  .flex-1.overflow-hidden {
    height: calc(100vh - 50px - 70px);
    height: calc(100dvh - 50px - 70px);
  }
}

/* 侧边栏遮罩层 */
@media (max-width: 768px) {
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;

    &.active {
      opacity: 1;
      visibility: visible;
    }
  }
}

/* 确保移动端输入框不会被虚拟键盘遮挡 */
@media (max-width: 768px) {
  .chat-input-container {
    position: sticky;
    bottom: 0;
    background: white;
    z-index: 100;
  }

  /* iOS Safari 适配 */
  @supports (-webkit-touch-callout: none) {
    .chat-input-container {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}

/* 滚动条优化 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
  }
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* API配置对话框样式 */
:deep(.api-config-dialog) {
  .el-dialog__body {
    padding: 0;
  }
}
</style>
