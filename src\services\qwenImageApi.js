/**
 * Qwen Image API 服务
 * 专门用于图像生成的API服务
 * 支持流式响应和reasoning_content
 */

import { BaseOpenAIApi } from './BaseOpenAIApi.js'
import { ElMessage } from 'element-plus'

class QwenImageApi extends BaseOpenAIApi {
  constructor() {
    super({
      baseUrl: 'http://*************:8020',
      apiKey: '', // Qwen-image不需要API Key
      defaultModel: 'qwen-plus',
      timeout: 120000, // 图像生成需要更长时间
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // Qwen-image支持的模型列表
    this.models = [
      {
        id: 'qwen-plus',
        name: 'Qwen Plus Image',
        description: 'Qwen Plus图像生成模型，支持高质量图像生成',
        maxTokens: 8000,
        supportsFunctions: false,
        supportsVision: false,
        supportsImageGeneration: true,
        pricing: '免费'
      }
    ]
  }

  /**
   * 重写makeRequest方法，因为Qwen-image不需要Authorization header
   */
  async makeRequest(url, options = {}) {
    let lastError = null
    const startTime = Date.now()

    for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

      try {
        const response = await fetch(url, {
          ...options,
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            ...this.config.headers,
            ...options.headers
          }
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorText = await response.text()
          let errorData
          try {
            errorData = JSON.parse(errorText)
          } catch {
            errorData = { message: errorText }
          }

          const error = new Error(
            errorData.error?.message || `API请求失败: ${response.status} ${response.statusText}`
          )
          error.status = response.status
          error.details = errorData

          // 根据错误类型决定是否重试
          if (this.shouldRetry(response.status, attempt)) {
            lastError = error
            if (attempt < this.config.maxRetries - 1) {
              await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)))
              continue
            }
          }

          throw error
        }

        return response

      } catch (error) {
        clearTimeout(timeoutId)

        if (error.name === 'AbortError') {
          lastError = new Error('请求超时，请稍后重试')
        } else {
          lastError = error
        }

        if (attempt < this.config.maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)))
          continue
        }
      }
    }

    throw lastError || new Error('请求失败')
  }

  /**
   * 获取可用模型列表
   */
  async getModels() {
    try {
      console.log('📋 获取Qwen-image模型列表...')
      
      return {
        success: true,
        data: this.models,
        message: '获取模型列表成功'
      }
    } catch (error) {
      console.error('❌ 获取Qwen-image模型列表失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.message
      }
    }
  }

  /**
   * 发送图像生成请求
   */
  async createChatCompletion(params) {
    try {
      console.log('🎨 发送Qwen-image生成请求:', {
        model: params.model || this.config.defaultModel,
        messageCount: params.messages?.length,
        stream: params.stream
      })

      // 构建请求数据，包含user字段
      const requestData = {
        model: params.model || this.config.defaultModel,
        messages: params.messages,
        stream: params.stream || false,
        user: params.user || `session-${Date.now()}`
      }

      // 发送请求
      const response = await this.makeRequest(`${this.config.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const data = await response.json()
      
      console.log('✅ Qwen-image请求成功')
      
      // 检查是否包含图片
      const content = data.choices?.[0]?.message?.content || ''
      const hasImage = this.extractImageFromContent(content)
      
      return {
        success: true,
        data: data,
        content: content,
        hasImage: !!hasImage,
        image: hasImage,
        message: '请求成功'
      }
    } catch (error) {
      console.error('❌ Qwen-image请求失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.message
      }
    }
  }

  /**
   * 流式图像生成请求
   */
  async createChatCompletionStream(params, onMessage, onError, onComplete) {
    try {
      console.log('🌊 发送Qwen-image流式生成请求...')

      const requestData = {
        model: params.model || this.config.defaultModel,
        messages: params.messages,
        stream: true,
        user: params.user || `session-${Date.now()}`
      }

      const response = await this.makeRequest(`${this.config.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''
      let fullContent = ''
      let reasoningContent = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            const trimmed = line.trim()
            if (trimmed === '' || trimmed === 'data: [DONE]') {
              if (trimmed === 'data: [DONE]') {
                onComplete?.()
                return
              }
              continue
            }
            
            if (trimmed.startsWith('data: ')) {
              try {
                const jsonStr = trimmed.slice(6)
                const data = JSON.parse(jsonStr)
                
                if (data.choices && data.choices[0] && data.choices[0].delta) {
                  const delta = data.choices[0].delta
                  
                  // 处理reasoning_content（生成过程信息）
                  if (delta.reasoning_content) {
                    reasoningContent += delta.reasoning_content
                    // 可以选择是否显示reasoning内容
                    // onMessage?.(delta.reasoning_content)
                  }
                  
                  // 处理实际内容（通常包含图片）
                  if (delta.content) {
                    fullContent += delta.content
                    onMessage?.(delta.content)
                  }
                }
              } catch (parseError) {
                console.warn('解析流数据失败:', parseError)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      onComplete?.()
    } catch (error) {
      console.error('❌ Qwen-image流式生成失败:', error)
      onError?.(error)
    }
  }

  /**
   * 从内容中提取图片URL
   */
  extractImageFromContent(content) {
    if (!content) return null
    
    // 匹配Markdown图片格式 ![alt](url)
    const imageRegex = /!\[.*?\]\((.*?)\)/
    const match = content.match(imageRegex)
    
    if (match && match[1]) {
      return match[1]
    }
    
    return null
  }

  /**
   * 获取错误信息
   */
  getErrorMessage(error) {
    if (error.status) {
      switch (error.status) {
        case 400:
          return '请求参数错误'
        case 401:
          return '未授权访问'
        case 403:
          return '访问被拒绝'
        case 404:
          return 'API端点不存在'
        case 429:
          return '请求频率过高，请稍后重试'
        case 500:
          return 'Qwen-image服务器内部错误'
        case 502:
        case 503:
        case 504:
          return 'Qwen-image服务暂时不可用'
        default:
          return error.details?.message || `请求失败 (${error.status})`
      }
    } else if (error.message?.includes('Failed to fetch')) {
      return '无法连接到Qwen-image服务器，请检查网络连接'
    } else if (error.message?.includes('timeout')) {
      return '图像生成超时，请稍后重试'
    } else {
      return error.message || '未知错误'
    }
  }

  /**
   * 测试API连接
   */
  async testConnection() {
    try {
      console.log('🔍 测试Qwen-image API连接...')

      const testMessages = [
        { role: 'user', content: '画一个简单的图案' }
      ]

      const result = await this.createChatCompletion({
        model: 'qwen-plus',
        messages: testMessages,
        user: `test-${Date.now()}`
      })

      if (result.success) {
        console.log('✅ Qwen-image API连接测试成功')
        return {
          success: true,
          message: 'Qwen-image API连接正常',
          response: result.data
        }
      } else {
        return result
      }
    } catch (error) {
      console.error('❌ Qwen-image API连接测试失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.message
      }
    }
  }

  /**
   * 生成图片（专用接口）
   */
  async generateImage(prompt, options = {}) {
    try {
      console.log('🎨 使用Qwen-image生成图片:', prompt)

      const messages = [
        { role: 'user', content: prompt }
      ]

      const result = await this.createChatCompletion({
        model: 'qwen-plus',
        messages: messages,
        user: options.user || `session-${Date.now()}`
      })

      if (result.success && result.hasImage) {
        return {
          success: true,
          imageUrl: result.image,
          content: result.content
        }
      } else {
        return {
          success: false,
          message: '未能生成图片',
          content: result.content
        }
      }
    } catch (error) {
      console.error('❌ Qwen-image生成图片失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.message
      }
    }
  }

  /**
   * 流式生成图片
   */
  async generateImageStream(prompt, options = {}, onProgress, onComplete, onError) {
    try {
      console.log('🌊 使用Qwen-image流式生成图片:', prompt)

      const messages = [
        { role: 'user', content: prompt }
      ]

      let fullContent = ''
      let hasReceivedImage = false

      await this.createChatCompletionStream(
        {
          model: 'qwen-plus',
          messages: messages,
          user: options.user || `session-${Date.now()}`
        },
        // onMessage
        (content) => {
          fullContent += content
          
          // 检查是否收到了图片
          const imageUrl = this.extractImageFromContent(fullContent)
          if (imageUrl && !hasReceivedImage) {
            hasReceivedImage = true
            onProgress?.({
              type: 'image',
              content: fullContent,
              imageUrl: imageUrl
            })
          } else {
            onProgress?.({
              type: 'text',
              content: content
            })
          }
        },
        // onError
        (error) => {
          console.error('❌ Qwen-image流式生成失败:', error)
          onError?.(error)
        },
        // onComplete
        () => {
          console.log('✅ Qwen-image流式生成完成')
          const imageUrl = this.extractImageFromContent(fullContent)
          onComplete?.({
            success: true,
            content: fullContent,
            hasImage: !!imageUrl,
            imageUrl: imageUrl
          })
        }
      )
    } catch (error) {
      console.error('❌ Qwen-image流式生成失败:', error)
      onError?.(error)
    }
  }
}

// 创建单例实例
const qwenImageApi = new QwenImageApi()

export default qwenImageApi