/**
 * Doubao AI API 服务
 * 支持对话和生图功能的多模态模型
 * API兼容OpenAI格式
 */

import { BaseOpenAIApi } from './BaseOpenAIApi.js'
import { ElMessage } from 'element-plus'

class DoubaoApi extends BaseOpenAIApi {
  constructor() {
    super({
      baseUrl: 'http://*************:8000',
      apiKey: 'doubao_user_388b1a84', // 根据图2设置正确的API Key
      defaultModel: 'doubao-pro-32k',
      timeout: 60000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // Doubao支持的模型列表
    this.models = [
      {
        id: 'doubao-pro-32k',
        name: 'Doubao Pro 32K',
        description: '豆包AI专业版，支持对话和生图功能',
        maxTokens: 32000,
        supportsFunctions: true,
        supportsVision: true,
        supportsImageGeneration: true,
        pricing: '免费'
      }
    ]
  }

  /**
   * 重写makeRequest方法，添加正确的Authorization header
   */
  async makeRequest(url, options = {}) {
    let lastError = null
    const startTime = Date.now()

    for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

      try {
        const response = await fetch(url, {
          ...options,
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.config.apiKey}`,
            ...this.config.headers,
            ...options.headers
          }
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorText = await response.text()
          let errorData
          try {
            errorData = JSON.parse(errorText)
          } catch {
            errorData = { message: errorText }
          }

          const error = new Error(
            errorData.error?.message || `API请求失败: ${response.status} ${response.statusText}`
          )
          error.status = response.status
          error.details = errorData

          // 根据错误类型决定是否重试
          if (this.shouldRetry(response.status, attempt)) {
            lastError = error
            if (attempt < this.config.maxRetries - 1) {
              await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)))
              continue
            }
          }

          throw error
        }

        return response

      } catch (error) {
        clearTimeout(timeoutId)

        if (error.name === 'AbortError') {
          lastError = new Error('请求超时，请稍后重试')
        } else {
          lastError = error
        }

        if (attempt < this.config.maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * (attempt + 1)))
          continue
        }
      }
    }

    throw lastError || new Error('请求失败')
  }

  /**
   * 获取可用模型列表
   */
  async getModels() {
    try {
      console.log('📋 获取Doubao模型列表...')
      
      return {
        success: true,
        data: this.models,
        message: '获取模型列表成功'
      }
    } catch (error) {
      console.error('❌ 获取Doubao模型列表失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.message
      }
    }
  }

  /**
   * 发送聊天请求（支持生图）
   */
  async createChatCompletion(params) {
    try {
      console.log('🤖 发送Doubao聊天请求:', {
        model: params.model || this.config.defaultModel,
        messageCount: params.messages?.length,
        temperature: params.temperature,
        stream: params.stream
      })

      // 验证和修复消息格式
      const validatedMessages = this.validateAndFixMessages(params.messages)

      // 构建请求数据
      const requestData = {
        model: params.model || this.config.defaultModel,
        messages: validatedMessages,
        temperature: params.temperature || 0.7,
        stream: params.stream || false
      }

      // 如果需要max_tokens，添加它
      if (params.max_tokens) {
        requestData.max_tokens = params.max_tokens
      }

      // 发送请求
      const response = await this.makeRequest(`${this.config.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const data = await response.json()
      
      console.log('✅ Doubao聊天请求成功')
      
      // 检查是否包含图片
      const content = data.choices?.[0]?.message?.content || ''
      const hasImage = this.extractImageFromContent(content)
      
      return {
        success: true,
        data: data,
        content: content,
        hasImage: !!hasImage,
        image: hasImage,
        message: '请求成功'
      }
    } catch (error) {
      console.error('❌ Doubao聊天请求失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.message
      }
    }
  }

  /**
   * 验证和修复消息格式
   */
  validateAndFixMessages(messages) {
    if (!Array.isArray(messages)) {
      console.warn('⚠️ 消息不是数组格式，转换为数组')
      return [{ role: 'user', content: String(messages || '') }]
    }

    return messages.map((message, index) => {
      // 如果消息是字符串，转换为标准格式
      if (typeof message === 'string') {
        return { role: 'user', content: message }
      }

      // 如果消息是对象但缺少role字段
      if (typeof message === 'object' && message !== null) {
        if (!message.role) {
          // 根据内容推断角色
          const content = message.content || ''
          if (content.includes('已切换到') || content.includes('系统')) {
            return { role: 'system', content: content }
          } else {
            return { role: index === 0 ? 'user' : 'assistant', content: content }
          }
        }

        // 确保content字段存在
        if (!message.content) {
          return { ...message, content: '' }
        }

        return message
      }

      // 如果消息格式完全错误，创建默认消息
      console.warn('⚠️ 消息格式错误，使用默认格式:', message)
      return { role: 'user', content: String(message || '') }
    }).filter(msg => msg.content.trim() !== '') // 过滤空消息
  }

  /**
   * 流式聊天请求
   */
  async createChatCompletionStream(params, onMessage, onError, onComplete) {
    try {
      console.log('🌊 发送Doubao流式聊天请求...')

      // 验证和修复消息格式
      const validatedMessages = this.validateAndFixMessages(params.messages)

      const requestData = {
        model: params.model || this.config.defaultModel,
        messages: validatedMessages,
        temperature: params.temperature || 0.7,
        stream: true
      }

      if (params.max_tokens) {
        requestData.max_tokens = params.max_tokens
      }

      const response = await this.makeRequest(`${this.config.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            const trimmed = line.trim()
            if (trimmed === '' || trimmed === 'data: [DONE]') continue
            
            if (trimmed.startsWith('data: ')) {
              try {
                const jsonStr = trimmed.slice(6)
                const data = JSON.parse(jsonStr)
                
                if (data.choices && data.choices[0] && data.choices[0].delta) {
                  const content = data.choices[0].delta.content
                  if (content) {
                    onMessage?.(content)
                  }
                }
              } catch (parseError) {
                console.warn('解析流数据失败:', parseError)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      onComplete?.()
    } catch (error) {
      console.error('❌ Doubao流式聊天失败:', error)
      onError?.(error)
    }
  }

  /**
   * 从内容中提取图片URL
   */
  extractImageFromContent(content) {
    if (!content) return null
    
    // 匹配Markdown图片格式 ![alt](url)
    const imageRegex = /!\[.*?\]\((.*?)\)/
    const match = content.match(imageRegex)
    
    if (match && match[1]) {
      return match[1]
    }
    
    return null
  }

  /**
   * 解析Doubao响应中的特殊命令
   */
  parseDoubaoCommands(content) {
    const commands = []
    
    // 匹配命令格式: 命令: 参数 (如 q: 清晰 1754453316 1)
    const lines = content.split('\n')
    for (const line of lines) {
      const trimmed = line.trim()
      
      // 匹配格式如: q: 清晰 1754453316 1
      if (trimmed.match(/^[a-z]: .+/)) {
        const [command, ...params] = trimmed.split(': ')
        const commandMap = {
          'q': '清晰',
          'v': '编辑',
          'k': '扩图',
          's': '动起来',
          'r': '重新生成'
        }
        commands.push({
          type: command,
          name: commandMap[command] || command,
          params: params.join(': ')
        })
      }
    }
    
    return commands
  }

  /**
   * 获取错误信息
   */
  getErrorMessage(error) {
    if (error.status) {
      switch (error.status) {
        case 400:
          return '请求参数错误'
        case 401:
          return '未授权访问'
        case 403:
          return '访问被拒绝'
        case 404:
          return 'API端点不存在'
        case 429:
          return '请求频率过高，请稍后重试'
        case 500:
          return 'Doubao服务器内部错误'
        case 502:
        case 503:
        case 504:
          return 'Doubao服务暂时不可用'
        default:
          return error.details?.message || `请求失败 (${error.status})`
      }
    } else if (error.message?.includes('Failed to fetch')) {
      return '无法连接到Doubao服务器，请检查网络连接'
    } else if (error.message?.includes('timeout')) {
      return '请求超时，请稍后重试'
    } else {
      return error.message || '未知错误'
    }
  }

  /**
   * 测试API连接
   */
  async testConnection() {
    try {
      console.log('🔍 测试Doubao API连接...')

      const testMessages = [
        { role: 'user', content: '你好' }
      ]

      const result = await this.createChatCompletion({
        model: 'doubao-pro-32k',
        messages: testMessages,
        max_tokens: 50,
        temperature: 0.1
      })

      if (result.success) {
        console.log('✅ Doubao API连接测试成功')
        return {
          success: true,
          message: 'Doubao API连接正常',
          response: result.data
        }
      } else {
        return result
      }
    } catch (error) {
      console.error('❌ Doubao API连接测试失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.message
      }
    }
  }

  /**
   * 生成图片（通过对话接口）
   */
  async generateImage(prompt, options = {}) {
    try {
      console.log('🎨 使用Doubao生成图片:', prompt)

      const messages = [
        { role: 'user', content: prompt }
      ]

      const result = await this.createChatCompletion({
        model: 'doubao-pro-32k',
        messages: messages,
        temperature: options.temperature || 0.7
      })

      if (result.success && result.hasImage) {
        return {
          success: true,
          imageUrl: result.image,
          content: result.content,
          commands: this.parseDoubaoCommands(result.content)
        }
      } else {
        return {
          success: false,
          message: '未能生成图片',
          content: result.content
        }
      }
    } catch (error) {
      console.error('❌ Doubao生成图片失败:', error)
      return {
        success: false,
        message: this.getErrorMessage(error),
        error: error.message
      }
    }
  }
}

// 创建单例实例
const doubaoApi = new DoubaoApi()

export default doubaoApi