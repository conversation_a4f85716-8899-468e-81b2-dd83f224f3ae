// 导入Sass模块
@use 'sass:map';
@use 'sass:meta';

// 响应式断点
$breakpoints: (
  xs: 320px,
  sm: 480px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  xxl: 1536px
);

// 响应式混合器
@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (max-width: map.get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin respond-above($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $min-width: map.get($breakpoints, $breakpoint) + 1px;
    @media (min-width: #{$min-width}) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin respond-between($min-breakpoint, $max-breakpoint) {
  @if map.has-key($breakpoints, $min-breakpoint) and map.has-key($breakpoints, $max-breakpoint) {
    $min-width: map.get($breakpoints, $min-breakpoint) + 1px;
    $max-width: map.get($breakpoints, $max-breakpoint);
    @media (min-width: #{$min-width}) and (max-width: #{$max-width}) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$min-breakpoint} or #{$max-breakpoint}";
  }
}

// 移动端优化混合器
@mixin mobile-optimized {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-overflow-scrolling: touch;
  touch-action: manipulation;
}

@mixin touch-target($size: 44px) {
  min-width: $size;
  min-height: $size;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 安全区域适配
@mixin safe-area-padding($property: padding, $direction: all) {
  @if $direction == all {
    #{$property}: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  } @else if $direction == top {
    #{$property}-top: env(safe-area-inset-top);
  } @else if $direction == bottom {
    #{$property}-bottom: env(safe-area-inset-bottom);
  } @else if $direction == left {
    #{$property}-left: env(safe-area-inset-left);
  } @else if $direction == right {
    #{$property}-right: env(safe-area-inset-right);
  }
}

// 文本截断
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-truncate-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 居中布局
@mixin center-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin center-absolute {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 阴影效果
@mixin card-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  } @else if $level == 2 {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  } @else if $level == 3 {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  } @else if $level == 4 {
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  }
}

// 渐变背景
@mixin gradient-bg($direction: 135deg, $color1: #667eea, $color2: #764ba2) {
  background: linear-gradient($direction, $color1 0%, $color2 100%);
}

// 动画过渡
@mixin smooth-transition($property: all, $duration: 0.3s, $timing: ease) {
  transition: $property $duration $timing;
}

// 工具类
.mobile-only {
  @include respond-above(md) {
    display: none !important;
  }
}

.desktop-only {
  @include respond-to(md) {
    display: none !important;
  }
}

.touch-target {
  @include touch-target;
}

.mobile-optimized {
  @include mobile-optimized;
}

.text-truncate {
  @include text-truncate;
}

.center-flex {
  @include center-flex;
}

// 显示/隐藏工具类
@each $breakpoint-name, $breakpoint-value in $breakpoints {
  .hidden-#{$breakpoint-name} {
    @include respond-to($breakpoint-name) {
      display: none !important;
    }
  }
  
  .visible-#{$breakpoint-name} {
    @include respond-to($breakpoint-name) {
      display: block !important;
    }
  }
}
