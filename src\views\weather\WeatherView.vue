<template>
  <div class="weather-app" :class="`bg-${activeWeather}`">
    <!-- 顶部导航 -->
    <header class="header">
      <button class="back-btn" @click="$router.back()">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回
      </button>
      <h1 class="title">天气</h1>
      <div class="spacer"></div>
    </header>

    <!-- 统一的全屏天气背景，固定在视口，置于内容之下 -->
    <div v-if="activeWeather === 'sunny'" class="global-bg sunny-global" aria-hidden="true">
      <div class="global-sun"></div>
      <div class="global-glow"></div>
      <div class="global-dust" v-for="n in 10" :key="'s'+n" :style="{ '--delay': n * 0.35 + 's' }"></div>
    </div>
    <div v-else-if="activeWeather === 'windy'" class="global-bg windy-global" aria-hidden="true">
      <div class="global-wind" v-for="n in 8" :key="'w'+n" :style="{ '--delay': n * 0.25 + 's', top: (10 + n*8) + '%' }"></div>
      <div class="global-cloud gc-1"></div>
      <div class="global-cloud gc-2"></div>
    </div>
    <div v-else-if="activeWeather === 'storm'" class="global-bg storm-global" aria-hidden="true">
      <div class="global-rain" v-for="n in 60" :key="'r'+n" :style="{ '--delay': (n%10)*0.1 + 's', left: (n*1.5)%100 + '%' }"></div>
      <div class="global-flash"></div>
      <div class="global-mist"></div>
    </div>
    <div v-else-if="activeWeather === 'blizzard'" class="global-bg snow-global" aria-hidden="true">
      <div class="global-snow" v-for="n in 40" :key="'sn'+n" :style="{ '--delay': (n%8)*0.2 + 's', left: (n*2.3)%100 + '%' }"></div>
      <div class="global-haze"></div>
    </div>

    <!-- 体积光/雨幕/雪场景 Canvas（固定在视口，作为全屏背景的动态层） -->
    <canvas ref="fxCanvas" class="fx-canvas" aria-hidden="true"></canvas>

    <!-- 天气卡片容器 -->
    <main class="cards-container" role="region" aria-label="天气卡片列表">

      <!-- 实时天气卡片 -->
      <div class="weather-card realtime"
           :class="{ active: activeWeather === 'realtime', loading: realTimeWeather.isLoading }"
           role="group"
           aria-roledescription="实时天气卡片"
           :aria-label="`实时天气，${locationData.city}，温度 ${realTimeWeather.data?.temp || '--'} 度`"
           tabindex="0"
           @mouseenter="setActive('realtime')"
           @click="selectWeather('realtime')"
           @keydown.enter.prevent="scrollToWeather('realtime')"
           @keydown.space.prevent="scrollToWeather('realtime')">

        <div class="card-bg realtime-bg">
          <!-- 加载动画 -->
          <div v-if="realTimeWeather.isLoading" class="loading-overlay">
            <div class="loading-spinner-large"></div>
            <div class="loading-text">获取实时天气中...</div>
          </div>

          <!-- 天气背景效果 -->
          <div v-if="realTimeWeather.data" class="weather-effects">
            <div class="weather-particles">
              <div v-for="n in 8" :key="n" class="particle" :style="{ '--delay': n * 0.2 + 's' }"></div>
            </div>
          </div>
        </div>

        <div class="card-content">
          <div class="weather-info">
            <div class="icon-section">
              <div class="weather-icon realtime-icon">
                <div v-if="realTimeWeather.data" class="weather-emoji">
                  {{ getWeatherEmoji(realTimeWeather.data.icon) }}
                </div>
                <div v-else-if="realTimeWeather.error" class="error-icon">❌</div>
                <div v-else class="placeholder-icon">🌤️</div>
              </div>
            </div>
            <div class="temp-section">
              <div class="temperature">
                {{ realTimeWeather.data?.temp || '--' }}°
              </div>
              <div class="weather-type">
                {{ realTimeWeather.data?.text || (realTimeWeather.error ? '获取失败' : '实时天气') }}
              </div>
              <div class="location">{{ locationData.city }}{{ locationData.district ? ' · ' + locationData.district : '' }}</div>
            </div>
          </div>

          <div class="details">
            <div v-if="realTimeWeather.data" class="detail-grid">
              <div class="detail">
                <span class="label">体感</span>
                <span class="value">{{ realTimeWeather.data.feelsLike }}°</span>
              </div>
              <div class="detail">
                <span class="label">湿度</span>
                <span class="value">{{ realTimeWeather.data.humidity }}%</span>
              </div>
              <div class="detail">
                <span class="label">风向</span>
                <span class="value">{{ realTimeWeather.data.windDir }}</span>
              </div>
              <div class="detail">
                <span class="label">风速</span>
                <span class="value">{{ realTimeWeather.data.windSpeed }} km/h</span>
              </div>
              <div class="detail">
                <span class="label">气压</span>
                <span class="value">{{ realTimeWeather.data.pressure }} hPa</span>
              </div>
              <div class="detail">
                <span class="label">能见度</span>
                <span class="value">{{ realTimeWeather.data.vis }} km</span>
              </div>
            </div>
            <div v-else-if="realTimeWeather.error" class="error-message">
              <span class="error-text">{{ realTimeWeather.error }}</span>
              <button class="error-close-btn" @click="clearError">×</button>
            </div>
            <div v-else class="placeholder-message">
              点击"定位"按钮获取实时天气
            </div>
          </div>

          <div v-if="realTimeWeather.lastUpdate" class="update-time">
            更新时间: {{ formatUpdateTime(realTimeWeather.lastUpdate) }}
          </div>
        </div>
      </div>

      <!-- 晴天卡片 -->
      <div class="weather-card sunny"
           :class="{ active: activeWeather === 'sunny' }"
           role="group"
           aria-roledescription="天气卡片"
           :aria-label="`晴天，${locationData.city}，温度 ${temps.sunny} 度`"
           tabindex="0"
           @mouseenter="setActive('sunny')"
           @click="selectWeather('sunny')"
           @keydown.enter.prevent="scrollToWeather('sunny')"
           @keydown.space.prevent="scrollToWeather('sunny')">
        
        <!-- 背景动画 -->
        <div class="card-bg sunny-bg">
          <div class="sun-rays">
            <div v-for="n in 8" :key="n" class="ray" :style="{ '--angle': n * 45 + 'deg' }"></div>
          </div>
          <div class="particles">
            <div v-for="n in 6" :key="n" class="particle" :style="{ '--delay': n * 0.5 + 's' }"></div>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="weather-info">
            <div class="icon-section">
              <div class="weather-icon sun-icon">
                <div class="sun-core"></div>
                <div class="sun-glow"></div>
              </div>
            </div>
            <div class="temp-section">
              <div class="temperature">{{ temps.sunny }}°</div>
              <div class="weather-type">晴天</div>
              <div class="location">{{ locationData.city }}{{ locationData.district ? ' · ' + locationData.district : '' }}</div>
            </div>
          </div>
          
          <div class="details">
            <div class="detail">
              <span class="label">体感</span>
              <span class="value">{{ temps.sunny - 1 }}°</span>
            </div>
            <div class="detail">
              <span class="label">湿度</span>
              <span class="value">35%</span>
            </div>
            <div class="detail">
              <span class="label">风速</span>
              <span class="value">3 m/s</span>
            </div>
            <div class="detail">
              <span class="label">紫外线</span>
              <span class="value">强</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 大风卡片 -->
      <div class="weather-card windy"
           :class="{ active: activeWeather === 'windy' }"
           role="group"
           aria-roledescription="天气卡片"
           :aria-label="`大风，${locationData.city}，温度 ${temps.windy} 度`"
           tabindex="0"
           @mouseenter="setActive('windy')"
           @click="selectWeather('windy')"
           @keydown.enter.prevent="scrollToWeather('windy')"
           @keydown.space.prevent="scrollToWeather('windy')">
        
        <div class="card-bg windy-bg">
          <div class="wind-lines">
            <div v-for="n in 5" :key="n" class="wind-line" :style="{ '--delay': n * 0.3 + 's' }"></div>
          </div>
          <div class="clouds">
            <div class="cloud cloud-1"></div>
            <div class="cloud cloud-2"></div>
          </div>
        </div>

        <div class="card-content">
          <div class="weather-info">
            <div class="icon-section">
              <div class="weather-icon wind-icon">
                <div class="wind-container">
                  <!-- 外层风旋 -->
                  <div class="wind-spiral outer"></div>
                  <!-- 中层风旋 -->
                  <div class="wind-spiral middle"></div>
                  <!-- 内层风旋 -->
                  <div class="wind-spiral inner"></div>
                  <!-- 风线效果 -->
                  <div v-for="n in 6" :key="n" class="wind-line" :style="{ '--angle': n * 60 + 'deg', '--delay': n * 0.1 + 's' }"></div>
                  <!-- 中心点 -->
                  <div class="wind-center"></div>
                </div>
              </div>
            </div>
            <div class="temp-section">
              <div class="temperature">{{ temps.windy }}°</div>
              <div class="weather-type">大风</div>
              <div class="location">{{ locationData.city }}{{ locationData.district ? ' · ' + locationData.district : '' }}</div>
            </div>
          </div>
          
          <div class="details">
            <div class="detail">
              <span class="label">阵风</span>
              <span class="value">18 m/s</span>
            </div>
            <div class="detail">
              <span class="label">湿度</span>
              <span class="value">40%</span>
            </div>
            <div class="detail">
              <span class="label">能见度</span>
              <span class="value">良好</span>
            </div>
            <div class="detail">
              <span class="label">风向</span>
              <span class="value">西北</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 暴雨卡片 -->
      <div class="weather-card storm"
           :class="{ active: activeWeather === 'storm' }"
           role="group"
           aria-roledescription="天气卡片"
           :aria-label="`暴雨，${locationData.city}，温度 ${temps.storm} 度`"
           tabindex="0"
           @mouseenter="setActive('storm')"
           @click="selectWeather('storm')"
           @keydown.enter.prevent="scrollToWeather('storm')"
           @keydown.space.prevent="scrollToWeather('storm')">
        
        <div class="card-bg storm-bg">
          <div class="rain-drops">
            <div v-for="n in 15" :key="n" class="drop" :style="{ '--delay': n * 0.1 + 's', '--offset': n * 8 + 'px' }"></div>
          </div>
          <div class="lightning"></div>
        </div>

        <div class="card-content">
          <div class="weather-info">
            <div class="icon-section">
              <div class="weather-icon rain-icon">
                <div class="rain-container">
                  <!-- 云朵层 -->
                  <div class="rain-cloud main"></div>
                  <div class="rain-cloud shadow"></div>
                  <!-- 闪电效果 -->
                  <div class="lightning"></div>
                  <!-- 雨滴流 -->
                  <div class="rain-streams">
                    <div v-for="n in 10" :key="n" class="stream"
                         :style="{
                           '--delay': n * 0.08 + 's',
                           '--offset': (n % 3) * 6 + 'px',
                           '--size': n % 2 === 0 ? 'heavy' : 'normal'
                         }"></div>
                  </div>
                  <!-- 水花效果 -->
                  <div class="rain-splash-container">
                    <div v-for="n in 3" :key="'splash'+n" class="rain-splash"
                         :style="{ '--delay': n * 0.4 + 's', '--position': n * 30 + 10 + '%' }"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="temp-section">
              <div class="temperature">{{ temps.storm }}°</div>
              <div class="weather-type">暴雨</div>
              <div class="location">{{ locationData.city }}{{ locationData.district ? ' · ' + locationData.district : '' }}</div>
            </div>
          </div>
          
          <div class="details">
            <div class="detail">
              <span class="label">降水量</span>
              <span class="value">45 mm/h</span>
            </div>
            <div class="detail">
              <span class="label">湿度</span>
              <span class="value">92%</span>
            </div>
            <div class="detail">
              <span class="label">雷暴</span>
              <span class="value">预警</span>
            </div>
            <div class="detail">
              <span class="label">能见度</span>
              <span class="value">差</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 暴雪卡片 -->
      <div class="weather-card snow"
           :class="{ active: activeWeather === 'blizzard' }"
           role="group"
           aria-roledescription="天气卡片"
           :aria-label="`暴雪，${locationData.city}，温度 ${temps.blizzard} 度`"
           tabindex="0"
           @mouseenter="setActive('blizzard')"
           @click="selectWeather('blizzard')"
           @keydown.enter.prevent="scrollToWeather('blizzard')"
           @keydown.space.prevent="scrollToWeather('blizzard')">
        
        <div class="card-bg snow-bg">
          <div class="snowflakes">
            <div v-for="n in 20" :key="n" class="snowflake" :style="{ '--delay': n * 0.2 + 's', '--offset': n * 6 + 'px' }">❄</div>
          </div>
        </div>

        <div class="card-content">
          <div class="weather-info">
            <div class="icon-section">
              <div class="weather-icon snow-icon">
                <div class="snow-crystal">
                  <div class="crystal-center"></div>
                  <!-- 主要臂膀 -->
                  <div v-for="n in 6" :key="n" class="crystal-arm main" :style="{ '--angle': n * 60 + 'deg' }"></div>
                  <!-- 次要臂膀 -->
                  <div v-for="n in 6" :key="'s'+n" class="crystal-arm secondary" :style="{ '--angle': n * 60 + 30 + 'deg' }"></div>
                  <!-- 分支结构 -->
                  <div v-for="n in 6" :key="'b'+n" class="crystal-branch" :style="{ '--angle': n * 60 + 'deg' }">
                    <div class="branch-left"></div>
                    <div class="branch-right"></div>
                  </div>
                  <!-- 装饰性结晶点 -->
                  <div v-for="n in 12" :key="'d'+n" class="crystal-dot" :style="{ '--angle': n * 30 + 'deg', '--distance': (n % 2 === 0 ? '15px' : '25px') }"></div>
                  <!-- 外层光环 -->
                  <div class="crystal-glow"></div>
                </div>
              </div>
            </div>
            <div class="temp-section">
              <div class="temperature">{{ temps.blizzard }}°</div>
              <div class="weather-type">暴雪</div>
              <div class="location">{{ locationData.city }}{{ locationData.district ? ' · ' + locationData.district : '' }}</div>
            </div>
          </div>
          
          <div class="details">
            <div class="detail">
              <span class="label">积雪</span>
              <span class="value">12 cm</span>
            </div>
            <div class="detail">
              <span class="label">体感</span>
              <span class="value">{{ temps.blizzard - 5 }}°</span>
            </div>
            <div class="detail">
              <span class="label">道路</span>
              <span class="value">结冰</span>
            </div>
            <div class="detail">
              <span class="label">能见度</span>
              <span class="value">极差</span>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 位置控制面板 -->
    <div class="location-control">
      <div class="location-info">
        <div class="current-location">
          <span class="location-icon">📍</span>
          <span class="location-text">
            {{ locationData.city }}{{ locationData.district ? ' · ' + locationData.district : '' }}
          </span>
        </div>
        <div v-if="locationData.error" class="location-error">
          {{ locationData.error }}
        </div>
      </div>

      <div class="location-actions">
        <button
          class="location-btn get-location"
          :class="{ loading: locationData.isLoading }"
          @click="getCurrentLocation"
          :disabled="locationData.isLoading"
          :title="locationData.isLoading ? '正在获取位置...' : '获取当前位置'"
        >
          <span v-if="locationData.isLoading" class="loading-spinner"></span>
          <span v-else class="location-icon">🎯</span>
          <span class="btn-text">{{ locationData.isLoading ? '定位中...' : '定位' }}</span>
        </button>

        <div class="manual-location">
          <input
            type="text"
            class="city-input"
            placeholder="手动输入城市"
            @keyup.enter="handleManualLocation"
            ref="cityInput"
          >
          <button
            class="location-btn set-location"
            @click="handleManualLocation"
            title="设置城市"
          >
            <span class="location-icon">✓</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <footer class="navigation">
      <div class="nav-dots" role="tablist" aria-label="天气类型导航">
        <button class="nav-dot"
                :class="{ active: activeWeather === 'realtime' }"
                @click="scrollToWeather('realtime')"
                role="tab"
                :aria-selected="activeWeather === 'realtime'"
                :tabindex="activeWeather === 'realtime' ? 0 : -1"
                aria-label="切换到实时天气">
          <span class="dot-ripple" aria-hidden="true"></span>
          <span class="dot-icon">🌍</span>
        </button>
        <button class="nav-dot"
                :class="{ active: activeWeather === 'sunny' }"
                @click="scrollToWeather('sunny')"
                role="tab"
                :aria-selected="activeWeather === 'sunny'"
                :tabindex="activeWeather === 'sunny' ? 0 : -1"
                aria-label="切换到晴天">
          <span class="dot-ripple" aria-hidden="true"></span>
          <span class="dot-icon">☀️</span>
        </button>
        <button class="nav-dot"
                :class="{ active: activeWeather === 'windy' }"
                @click="scrollToWeather('windy')"
                role="tab"
                :aria-selected="activeWeather === 'windy'"
                :tabindex="activeWeather === 'windy' ? 0 : -1"
                aria-label="切换到大风">
          <span class="dot-ripple" aria-hidden="true"></span>
          <span class="dot-icon">💨</span>
        </button>
        <button class="nav-dot"
                :class="{ active: activeWeather === 'storm' }"
                @click="scrollToWeather('storm')"
                role="tab"
                :aria-selected="activeWeather === 'storm'"
                :tabindex="activeWeather === 'storm' ? 0 : -1"
                aria-label="切换到暴雨">
          <span class="dot-ripple" aria-hidden="true"></span>
          <span class="dot-icon">⛈️</span>
        </button>
        <button class="nav-dot"
                :class="{ active: activeWeather === 'blizzard' }"
                @click="scrollToWeather('blizzard')"
                role="tab"
                :aria-selected="activeWeather === 'blizzard'"
                :tabindex="activeWeather === 'blizzard' ? 0 : -1"
                aria-label="切换到暴雪">
          <span class="dot-ripple" aria-hidden="true"></span>
          <span class="dot-icon">❄️</span>
        </button>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'

const activeWeather = ref('sunny')
const temps = reactive({
  sunny: 28,
  windy: 20,
  storm: 23,
  blizzard: -6
})

// 位置相关数据
const locationData = reactive({
  city: '北京',
  district: '',
  isLoading: false,
  error: null,
  coordinates: null,
  locationId: null // 和风天气的LocationID
})

// 实时天气数据
const realTimeWeather = reactive({
  isLoading: false,
  error: null,
  data: null,
  lastUpdate: null
})

// 和风天气API配置
const QWEATHER_CONFIG = {
  apiKey: 'dd7b284a59b146698f99a5deed8e0842',
  baseUrl: 'https://devapi.qweather.com/v7',
  // 备用免费API配置
  fallbackUrl: 'https://api.openweathermap.org/data/2.5'
}

const containerRef = ref(null)
const fxCanvas = ref(null)
const cityInput = ref(null)

function setActive(weather) {
  activeWeather.value = weather
}

function selectWeather(weather) {
  activeWeather.value = weather
  const card = document.querySelector(`.weather-card.${weather}`)
  if (card) {
    card.classList.add('tap')
    setTimeout(() => card.classList.remove('tap'), 200)
  }
}

function scrollToWeather(weather) {
  activeWeather.value = weather
  const card = document.querySelector(`.weather-card.${weather}`)
  if (card) {
    card.scrollIntoView({
      behavior: 'smooth',
      inline: 'center',
      block: 'nearest'
    })
    // 回弹动画：到位后轻微放大再回到1
    setTimeout(() => {
      card.classList.add('snap-bounce')
      setTimeout(() => card.classList.remove('snap-bounce'), 160)
    }, 420)
  }
}

// 计算最接近中心的卡片
function snapToNearest() {
  const el = containerRef.value
  if (!el) return
  const rect = el.getBoundingClientRect()
  const centerX = rect.left + rect.width / 2

  const cards = Array.from(el.querySelectorAll('.weather-card'))
  if (!cards.length) return

  let nearest = null
  let minDist = Infinity
  cards.forEach(card => {
    const r = card.getBoundingClientRect()
    const cardCenter = r.left + r.width / 2
    const dist = Math.abs(cardCenter - centerX)
    if (dist < minDist) {
      minDist = dist
      nearest = card
    }
  })

  if (nearest) {
    // 平滑吸附
    nearest.scrollIntoView({
      behavior: 'smooth',
      inline: 'center',
      block: 'nearest'
    })
    // 设置激活态
    const classes = nearest.classList
    if (classes.contains('realtime')) setActive('realtime')
    else if (classes.contains('sunny')) setActive('sunny')
    else if (classes.contains('windy')) setActive('windy')
    else if (classes.contains('storm')) setActive('storm')
    else if (classes.contains('snow')) setActive('blizzard')

    // 卡片回弹动画
    nearest.classList.add('snap-bounce')
    setTimeout(() => nearest.classList.remove('snap-bounce'), 160)
  }
}

// 防抖工具
let scrollTimer = null
function onScroll() {
  if (scrollTimer) clearTimeout(scrollTimer)
  scrollTimer = setTimeout(snapToNearest, 120)
}

function onKeydown(e) {
  const order = ['realtime', 'sunny', 'windy', 'storm', 'blizzard']
  const idx = order.indexOf(activeWeather.value)
  if (e.key === 'ArrowRight') {
    const next = order[Math.min(order.length - 1, idx + 1)]
    if (next && next !== activeWeather.value) scrollToWeather(next)
  } else if (e.key === 'ArrowLeft') {
    const prev = order[Math.max(0, idx - 1)]
    if (prev && prev !== activeWeather.value) scrollToWeather(prev)
  } else if (e.key === 'Home') {
    scrollToWeather(order[0])
  } else if (e.key === 'End') {
    scrollToWeather(order[order.length - 1])
  }
}

/* ===== Canvas FX：体积光/风线/雨幕/雪 ===== */
let ctx = null
let rafId = 0
let particles = []
let lastT = 0
let dpi = 1

function resizeCanvas() {
  const cvs = fxCanvas.value
  if (!cvs) return
  const rect = { w: window.innerWidth, h: window.innerHeight }
  dpi = Math.min(window.devicePixelRatio || 1, 2)
  cvs.width = Math.floor(rect.w * dpi)
  cvs.height = Math.floor(rect.h * dpi)
  cvs.style.width = rect.w + 'px'
  cvs.style.height = rect.h + 'px'
  ctx = cvs.getContext('2d', { alpha: true })
  if (ctx) {
    ctx.setTransform(dpi, 0, 0, dpi, 0, 0)
  }
}

function createParticlesFor(weather) {
  particles = []
  const w = window.innerWidth
  const h = window.innerHeight
  const rand = (a, b) => a + Math.random() * (b - a)

  if (weather === 'realtime') {
    // 实时天气粒子效果 - 根据天气数据动态生成
    if (realTimeWeather.data) {
      const iconCode = realTimeWeather.data.icon
      if (iconCode.startsWith('1')) {
        // 晴天粒子
        for (let i = 0; i < 80; i++) {
          particles.push({
            type: 'dust',
            x: rand(0, w),
            y: rand(0, h),
            r: rand(0.5, 1.2),
            a: rand(0.1, 0.3),
            vx: rand(-0.02, 0.02),
            vy: rand(-0.02, 0.02)
          })
        }
      } else if (iconCode.startsWith('3') || iconCode.startsWith('30')) {
        // 雨天粒子
        for (let i = 0; i < 100; i++) {
          particles.push({
            type: 'rain',
            x: rand(0, w),
            y: rand(-h, 0),
            v: rand(300, 500),
            a: rand(0.3, 0.7),
            drift: rand(-20, 20)
          })
        }
      } else if (iconCode.startsWith('4')) {
        // 雪天粒子
        for (let i = 0; i < 150; i++) {
          particles.push({
            type: 'snow',
            x: rand(0, w),
            y: rand(-h, 0),
            r: rand(1.5, 4),
            v: rand(30, 80),
            a: rand(0.4, 0.9),
            drift: rand(-15, 15)
          })
        }
      } else {
        // 默认微风粒子
        for (let i = 0; i < 30; i++) {
          particles.push({
            type: 'wind',
            y: rand(0, h * 0.7),
            x: rand(-w, w),
            len: rand(60, 150),
            a: rand(0.1, 0.25),
            v: rand(80, 150)
          })
        }
      }
    }
  } else if (weather === 'sunny') {
    // 体积光微尘粒子
    for (let i = 0; i < 120; i++) {
      particles.push({
        type: 'dust',
        x: rand(0, w),
        y: rand(0, h),
        r: rand(0.6, 1.6),
        a: rand(0.12, 0.35),
        vx: rand(-0.03, 0.03),
        vy: rand(-0.03, 0.03)
      })
    }
  } else if (weather === 'windy') {
    // 远景风线 + 漂浮尘
    for (let i = 0; i < 40; i++) {
      particles.push({
        type: 'wind',
        y: rand(0, h * 0.7),
        x: rand(-w, w),
        len: rand(80, 200),
        a: rand(0.15, 0.35),
        v: rand(120, 220) // px/s
      })
    }
    for (let i = 0; i < 80; i++) {
      particles.push({
        type: 'speck',
        x: rand(0, w), y: rand(0, h),
        r: rand(0.5, 1.2), a: rand(0.08, 0.2),
        vx: rand(10, 40), vy: rand(-10, 10) // 被风带动
      })
    }
  } else if (weather === 'storm') {
    // 全屏雨幕（不同速度/粗细）
    for (let i = 0; i < 220; i++) {
      const speed = rand(500, 1200) // px/s
      particles.push({
        type: 'rain',
        x: rand(0, w), y: rand(-h, h),
        len: rand(10, 32),
        a: rand(0.35, 0.75),
        v: speed,
        drift: rand(-80, -20)
      })
    }
  } else if (weather === 'blizzard') {
    // 多层雪（近/中/远不同大小与速度）+ 近景轻雾
    for (let i = 0; i < 160; i++) {
      const layer = Math.random()
      particles.push({
        type: 'snow',
        x: rand(0, w), y: rand(-h, h),
        r: layer < 0.2 ? rand(2.2, 3.2) : layer < 0.6 ? rand(1.4, 2.2) : rand(0.8, 1.4),
        a: layer < 0.2 ? 0.9 : layer < 0.6 ? 0.75 : 0.6,
        vy: layer < 0.2 ? rand(80, 120) : layer < 0.6 ? rand(50, 80) : rand(30, 50),
        vx: rand(-20, 20),
        sway: rand(0.8, 2.2),
        phi: rand(0, Math.PI * 2)
      })
    }
  }
}

function renderRealtime(dt) {
  const w = window.innerWidth, h = window.innerHeight

  // 根据实时天气数据动态渲染背景
  if (realTimeWeather.data) {
    const iconCode = realTimeWeather.data.icon

    // 根据天气图标代码选择渲染效果
    if (iconCode.startsWith('1')) {
      // 晴天类 (100-104)
      renderSunny(dt)
    } else if (iconCode.startsWith('3') || iconCode.startsWith('30')) {
      // 雨天类 (300-399)
      renderStorm(dt)
    } else if (iconCode.startsWith('4')) {
      // 雪天类 (400-499)
      renderBlizzard(dt, Date.now() / 1000)
    } else if (iconCode.startsWith('5')) {
      // 雾霾类 (500-515)
      const g = ctx.createLinearGradient(0, 0, 0, h)
      g.addColorStop(0, 'rgba(200,200,200,0.08)')
      g.addColorStop(1, 'rgba(100,100,100,0.03)')
      ctx.fillStyle = g
      ctx.fillRect(0, 0, w, h)
    } else {
      // 其他天气，使用多云效果
      renderWindy(dt)
    }
  } else {
    // 没有数据时显示默认效果
    const g = ctx.createLinearGradient(0, 0, 0, h)
    g.addColorStop(0, 'rgba(100,150,200,0.05)')
    g.addColorStop(1, 'rgba(0,0,0,0)')
    ctx.fillStyle = g
    ctx.fillRect(0, 0, w, h)
  }
}

function renderSunny(dt) {
  const w = window.innerWidth, h = window.innerHeight
  // 体积光：左上角暖色锥形渐变
  const gx = 0.18 * w, gy = 0.12 * h
  const g = ctx.createRadialGradient(gx, gy, 0, gx, gy, Math.max(w, h))
  g.addColorStop(0, 'rgba(255,220,120,0.20)')
  g.addColorStop(0.3, 'rgba(255,210,100,0.12)')
  g.addColorStop(1, 'rgba(255,200,90,0)')
  ctx.fillStyle = g
  ctx.fillRect(0, 0, w, h)

  // 微尘
  particles.forEach(p => {
    p.x += p.vx * dt
    p.y += p.vy * dt
    if (p.x < -10) p.x = w + 10
    if (p.x > w + 10) p.x = -10
    if (p.y < -10) p.y = h + 10
    if (p.y > h + 10) p.y = -10
    ctx.beginPath()
    ctx.fillStyle = `rgba(255,255,255,${p.a})`
    ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2)
    ctx.fill()
  })
}

function renderWindy(dt) {
  const w = window.innerWidth, h = window.innerHeight
  // 顶部偏冷蓝色体积感
  const g = ctx.createLinearGradient(0, 0, 0, h)
  g.addColorStop(0, 'rgba(150,200,255,0.06)')
  g.addColorStop(1, 'rgba(0,0,0,0)')
  ctx.fillStyle = g
  ctx.fillRect(0, 0, w, h)

  particles.forEach(p => {
    if (p.type === 'wind') {
      p.x += p.v * dt
      if (p.x > w + p.len) p.x = -p.len
      ctx.strokeStyle = `rgba(200,230,255,${p.a})`
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.moveTo(p.x, p.y)
      ctx.lineTo(p.x - p.len, p.y)
      ctx.stroke()
    } else {
      // speck
      p.x += p.vx * dt
      p.y += p.vy * dt * 0.2
      if (p.x > w + 10) p.x = -10
      if (p.y < -10) p.y = h + 10
      if (p.y > h + 10) p.y = -10
      ctx.fillStyle = `rgba(255,255,255,${p.a})`
      ctx.fillRect(p.x, p.y, p.r, p.r)
    }
  })
}

function renderStorm(dt) {
  const w = window.innerWidth, h = window.innerHeight
  // 背景冷色雾
  const g = ctx.createLinearGradient(0, 0, 0, h)
  g.addColorStop(0, 'rgba(200,220,255,0.05)')
  g.addColorStop(1, 'rgba(0,0,0,0)')
  ctx.fillStyle = g
  ctx.fillRect(0, 0, w, h)

  // 雨幕
  ctx.strokeStyle = 'rgba(180,210,255,0.6)'
  particles.forEach(p => {
    p.y += p.v * dt
    p.x += p.drift * dt * 0.2
    if (p.y > h + 20) { p.y = -20; p.x = Math.random() * w }
    if (p.x < -20) p.x = w + 20
    ctx.globalAlpha = p.a
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(p.x, p.y)
    ctx.lineTo(p.x, p.y - p.len)
    ctx.stroke()
    ctx.globalAlpha = 1
  })

  // 闪电微弱反射（与现有 CSS 闪电互补，不冲突）
  ctx.fillStyle = 'rgba(255,255,255,0.03)'
  ctx.fillRect(0, 0, w, Math.min(0.18 * h, 160))
}

function renderBlizzard(dt, t) {
  const w = window.innerWidth, h = window.innerHeight
  // 近地轻雾
  const g = ctx.createLinearGradient(0, h * 0.7, 0, h)
  g.addColorStop(0, 'rgba(255,255,255,0)')
  g.addColorStop(1, 'rgba(255,255,255,0.10)')
  ctx.fillStyle = g
  ctx.fillRect(0, h * 0.7, w, h * 0.3)

  particles.forEach(p => {
    p.phi += p.sway * dt * 0.6
    p.x += Math.sin(p.phi) * 14 * dt + p.vx * dt * 0.2
    p.y += p.vy * dt
    if (p.y > h + 10) { p.y = -10; p.x = Math.random() * w }
    if (p.x < -10) p.x = w + 10
    if (p.x > w + 10) p.x = -10
    ctx.beginPath()
    ctx.fillStyle = `rgba(255,255,255,${p.a})`
    ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2)
    ctx.fill()
  })
}

function tick(ts) {
  if (!ctx) return
  const t = ts / 1000
  const dt = Math.min(0.033, t - (lastT || t))
  lastT = t

  // 清屏（渐隐清除，带出体积叠加柔和效果）
  ctx.clearRect(0, 0, window.innerWidth, window.innerHeight)

  const w = activeWeather.value
  if (w === 'realtime') renderRealtime(dt)
  else if (w === 'sunny') renderSunny(dt)
  else if (w === 'windy') renderWindy(dt)
  else if (w === 'storm') renderStorm(dt)
  else if (w === 'blizzard') renderBlizzard(dt, t)

  rafId = requestAnimationFrame(tick)
}

function startFx() {
  resizeCanvas()
  createParticlesFor(activeWeather.value)
  lastT = 0
  cancelAnimationFrame(rafId)
  rafId = requestAnimationFrame(tick)
}

function stopFx() {
  cancelAnimationFrame(rafId)
}

watch(activeWeather, () => {
  createParticlesFor(activeWeather.value)
})

onMounted(() => {
  // 绑定容器引用
  containerRef.value = document.querySelector('.cards-container')
  if (containerRef.value) {
    containerRef.value.addEventListener('scroll', onScroll, { passive: true })
  }
  window.addEventListener('keydown', onKeydown)

  // Canvas FX
  startFx()
  window.addEventListener('resize', resizeCanvas)
  setTimeout(() => setActive('realtime'), 300)
})

// 地理定位相关函数
async function getCurrentLocation() {
  if (!navigator.geolocation) {
    locationData.error = '您的浏览器不支持地理定位'
    return
  }

  locationData.isLoading = true
  locationData.error = null

  try {
    const position = await new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5分钟缓存
      })
    })

    locationData.coordinates = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude
    }

    // 使用逆地理编码获取城市名称
    await reverseGeocode(position.coords.latitude, position.coords.longitude)

    // 获取当前位置的实时天气
    await getWeatherByCoordinates(position.coords.latitude, position.coords.longitude)

  } catch (error) {
    console.error('获取位置失败:', error)
    switch (error.code) {
      case error.PERMISSION_DENIED:
        locationData.error = '位置访问被拒绝，请在浏览器设置中允许位置访问'
        break
      case error.POSITION_UNAVAILABLE:
        locationData.error = '位置信息不可用'
        break
      case error.TIMEOUT:
        locationData.error = '获取位置超时，请重试'
        break
      default:
        locationData.error = '获取位置失败，请重试'
        break
    }
  } finally {
    locationData.isLoading = false
  }
}

// 逆地理编码 - 将坐标转换为城市名称
async function reverseGeocode(lat, lng) {
  try {
    // 使用高德地图API进行逆地理编码
    const response = await fetch(
      `https://restapi.amap.com/v3/geocode/regeo?key=YOUR_AMAP_KEY&location=${lng},${lat}&poitype=&radius=1000&extensions=base&batch=false&roadlevel=0`
    )

    if (!response.ok) {
      throw new Error('逆地理编码请求失败')
    }

    const data = await response.json()

    if (data.status === '1' && data.regeocode) {
      const addressComponent = data.regeocode.addressComponent
      locationData.city = addressComponent.city || addressComponent.province || '未知城市'
      locationData.district = addressComponent.district || ''
    } else {
      // 如果高德API失败，使用浏览器内置的逆地理编码（精度较低）
      await fallbackReverseGeocode(lat, lng)
    }
  } catch (error) {
    console.error('逆地理编码失败:', error)
    // 使用备用方案
    await fallbackReverseGeocode(lat, lng)
  }
}

// 备用逆地理编码方案
async function fallbackReverseGeocode(lat, lng) {
  try {
    // 使用免费的OpenStreetMap Nominatim API
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=zh-CN`
    )

    if (!response.ok) {
      throw new Error('备用逆地理编码请求失败')
    }

    const data = await response.json()

    if (data.address) {
      locationData.city = data.address.city ||
                         data.address.town ||
                         data.address.county ||
                         data.address.state ||
                         '未知城市'
      locationData.district = data.address.suburb ||
                             data.address.neighbourhood ||
                             ''
    } else {
      locationData.city = '未知城市'
      locationData.district = ''
    }
  } catch (error) {
    console.error('备用逆地理编码也失败了:', error)
    locationData.city = '定位失败'
    locationData.district = ''
  }
}

// 手动设置城市
function setManualLocation(city, district = '') {
  locationData.city = city
  locationData.district = district
  locationData.error = null
}

// 处理手动输入城市
function handleManualLocation() {
  const input = cityInput.value
  if (!input) return

  const cityName = input.value.trim()
  if (cityName) {
    setManualLocation(cityName)
    input.value = ''
    input.blur()
    // 获取新城市的天气数据
    searchLocationAndGetWeather(cityName)
  }
}

// 清除错误信息
function clearError() {
  realTimeWeather.error = null
}

// 和风天气API相关函数

// 根据城市名搜索LocationID
async function searchLocationAndGetWeather(cityName) {
  try {
    realTimeWeather.isLoading = true
    realTimeWeather.error = null

    // 先搜索城市获取LocationID
    const locationResponse = await fetch(
      `${QWEATHER_CONFIG.baseUrl}/city/lookup?location=${encodeURIComponent(cityName)}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'X-QW-Api-Key': QWEATHER_CONFIG.apiKey
        }
      }
    )

    if (!locationResponse.ok) {
      const errorText = await locationResponse.text()
      console.error('搜索城市API错误:', locationResponse.status, errorText)

      if (locationResponse.status === 403) {
        throw new Error('API访问被拒绝，请检查API密钥或账户状态')
      } else if (locationResponse.status === 401) {
        throw new Error('API密钥无效或已过期')
      } else if (locationResponse.status === 429) {
        throw new Error('请求过于频繁，请稍后再试')
      } else {
        throw new Error(`搜索城市失败 (${locationResponse.status})`)
      }
    }

    const locationData = await locationResponse.json()

    if (locationData.code !== '200' || !locationData.location || locationData.location.length === 0) {
      throw new Error('未找到该城市')
    }

    // 使用第一个搜索结果
    const location = locationData.location[0]
    locationData.locationId = location.id

    // 获取天气数据
    await Promise.all([
      getRealTimeWeather(location.id),
      getWeatherForecast(location.id)
    ])

  } catch (error) {
    console.error('搜索城市和获取天气失败:', error)
    realTimeWeather.error = error.message
  } finally {
    realTimeWeather.isLoading = false
  }
}

// 根据坐标获取LocationID和天气
async function getWeatherByCoordinates(lat, lng) {
  try {
    realTimeWeather.isLoading = true
    realTimeWeather.error = null

    // 使用坐标直接获取天气（和风天气支持坐标查询）
    await Promise.all([
      getRealTimeWeather(`${lng},${lat}`),
      getWeatherForecast(`${lng},${lat}`)
    ])

  } catch (error) {
    console.error('根据坐标获取天气失败:', error)
    realTimeWeather.error = error.message
  } finally {
    realTimeWeather.isLoading = false
  }
}

// 获取实时天气数据
async function getRealTimeWeather(location) {
  try {
    // 首先尝试和风天气API
    const response = await fetch(
      `${QWEATHER_CONFIG.baseUrl}/weather/now?location=${location}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'X-QW-Api-Key': QWEATHER_CONFIG.apiKey
        }
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error('实时天气API错误:', response.status, errorText)

      if (response.status === 403) {
        throw new Error('API访问被拒绝：可能是账户余额不足、API密钥无效或违反使用限制')
      } else if (response.status === 401) {
        throw new Error('API密钥认证失败')
      } else if (response.status === 429) {
        throw new Error('请求频率过高，请稍后再试')
      } else if (response.status === 400) {
        throw new Error('请求参数错误或数据不可用')
      } else {
        throw new Error(`获取天气数据失败 (${response.status})`)
      }
    }

    const data = await response.json()

    if (data.code !== '200') {
      if (data.code === '401') {
        throw new Error('API密钥无效')
      } else if (data.code === '402') {
        throw new Error('API使用次数已超限')
      } else if (data.code === '403') {
        throw new Error('API访问被禁止')
      } else if (data.code === '404') {
        throw new Error('未找到该地区的天气数据')
      }
      throw new Error(`天气API错误: ${data.code}`)
    }

    // 转换和风天气数据格式
    realTimeWeather.data = {
      temp: data.now.temp,
      feelsLike: data.now.feelsLike,
      text: data.now.text,
      icon: data.now.icon,
      windDir: data.now.windDir,
      windSpeed: data.now.windSpeed,
      humidity: data.now.humidity,
      pressure: data.now.pressure,
      vis: data.now.vis,
      obsTime: data.now.obsTime
    }
    realTimeWeather.lastUpdate = new Date()
    realTimeWeather.error = null

  } catch (error) {
    console.error('和风天气API失败:', error)

    // 如果和风天气失败，尝试使用模拟数据
    try {
      await getFallbackWeatherData(location)
      // 显示友好的提示信息
      realTimeWeather.error = '正在使用模拟天气数据（API访问受限）'
      console.warn('使用模拟天气数据:', error.message)
    } catch (fallbackError) {
      console.error('备用方案也失败:', fallbackError)
      realTimeWeather.error = `获取天气失败: ${error.message}`
      throw error
    }
  }
}

// 备用天气数据（模拟真实数据）
async function getFallbackWeatherData(location) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500))

  // 根据位置生成模拟数据
  const mockData = generateMockWeatherData(location)

  realTimeWeather.data = mockData
  realTimeWeather.lastUpdate = new Date()
  realTimeWeather.error = null
}

// 生成模拟天气数据
function generateMockWeatherData(location) {
  const weatherTypes = [
    { icon: '100', text: '晴', temp: 25 },
    { icon: '101', text: '多云', temp: 22 },
    { icon: '104', text: '阴', temp: 18 },
    { icon: '305', text: '小雨', temp: 16 },
    { icon: '400', text: '小雪', temp: -2 }
  ]

  // 根据位置和时间生成相对稳定的随机数
  const seed = location.toString().length + new Date().getDate()
  const weatherIndex = seed % weatherTypes.length
  const weather = weatherTypes[weatherIndex]

  const tempVariation = (seed % 10) - 5 // -5 到 +5 的变化

  return {
    temp: (weather.temp + tempVariation).toString(),
    feelsLike: (weather.temp + tempVariation + 2).toString(),
    text: weather.text,
    icon: weather.icon,
    windDir: '东南风',
    windSpeed: (8 + (seed % 15)).toString(),
    humidity: (60 + (seed % 30)).toString(),
    pressure: (1013 + (seed % 20) - 10).toString(),
    vis: (15 + (seed % 10)).toString(),
    obsTime: new Date().toISOString()
  }
}

// 获取天气图标对应的emoji
function getWeatherEmoji(iconCode) {
  const iconMap = {
    '100': '☀️', // 晴
    '101': '🌤️', // 多云
    '102': '⛅', // 少云
    '103': '☁️', // 晴间多云
    '104': '☁️', // 阴
    '300': '🌦️', // 阵雨
    '301': '🌦️', // 强阵雨
    '302': '⛈️', // 雷阵雨
    '303': '⛈️', // 强雷阵雨
    '304': '🌨️', // 雷阵雨伴有冰雹
    '305': '🌧️', // 小雨
    '306': '🌧️', // 中雨
    '307': '🌧️', // 大雨
    '308': '🌧️', // 极端降雨
    '309': '🌦️', // 毛毛雨/细雨
    '310': '🌧️', // 暴雨
    '311': '🌧️', // 大暴雨
    '312': '🌧️', // 特大暴雨
    '313': '🌨️', // 冻雨
    '314': '🌧️', // 小到中雨
    '315': '🌧️', // 中到大雨
    '316': '🌧️', // 大到暴雨
    '317': '🌧️', // 暴雨到大暴雨
    '318': '🌧️', // 大暴雨到特大暴雨
    '399': '🌧️', // 雨
    '400': '🌨️', // 小雪
    '401': '🌨️', // 中雪
    '402': '❄️', // 大雪
    '403': '❄️', // 暴雪
    '404': '🌨️', // 雨夹雪
    '405': '🌨️', // 小到中雪
    '406': '🌨️', // 中到大雪
    '407': '❄️', // 大到暴雪
    '408': '🌨️', // 小雨转雪
    '409': '🌨️', // 雪转雨
    '410': '🌨️', // 阵雪
    '499': '❄️', // 雪
    '500': '🌫️', // 薄雾
    '501': '🌫️', // 雾
    '502': '🌫️', // 霾
    '503': '💨', // 扬沙
    '504': '💨', // 浮尘
    '507': '💨', // 沙尘暴
    '508': '💨', // 强沙尘暴
    '509': '🌫️', // 浓雾
    '510': '🌫️', // 强浓雾
    '511': '🌫️', // 中度霾
    '512': '🌫️', // 重度霾
    '513': '🌫️', // 严重霾
    '514': '🌫️', // 大雾
    '515': '🌫️', // 特强浓雾
    '900': '🌡️', // 热
    '901': '🥶', // 冷
    '999': '❓'  // 未知
  }

  return iconMap[iconCode] || '🌤️'
}

// 获取天气预报数据
async function getWeatherForecast(location) {
  try {
    const response = await fetch(
      `${QWEATHER_CONFIG.baseUrl}/weather/7d?location=${location}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'X-QW-Api-Key': QWEATHER_CONFIG.apiKey
        }
      }
    )

    if (!response.ok) {
      if (response.status === 403) {
        throw new Error('API访问受限')
      }
      throw new Error(`HTTP错误: ${response.status}`)
    }

    const data = await response.json()

    if (data.code !== '200') {
      throw new Error(`预报API错误: ${data.code}`)
    }

    forecast.data = data.daily
    forecast.lastUpdate = new Date()
    forecast.error = null

  } catch (error) {
    console.error('获取天气预报失败:', error)

    // 使用备用预报数据
    try {
      await getFallbackForecastData(location)
      forecast.error = '正在使用模拟预报数据（API访问受限）'
      console.warn('使用模拟预报数据:', error.message)
    } catch (fallbackError) {
      forecast.error = `获取预报失败: ${error.message}`
      throw error
    }
  }
}

// 备用预报数据
async function getFallbackForecastData(location) {
  await new Promise(resolve => setTimeout(resolve, 300))

  const mockForecast = generateMockForecastData(location)
  forecast.data = mockForecast
  forecast.lastUpdate = new Date()
  forecast.error = null
}

// 生成模拟预报数据
function generateMockForecastData(location) {
  const weatherTypes = [
    { icon: '100', textDay: '晴', tempMax: 25, tempMin: 15 },
    { icon: '101', textDay: '多云', tempMax: 22, tempMin: 12 },
    { icon: '104', textDay: '阴', tempMax: 18, tempMin: 8 },
    { icon: '305', textDay: '小雨', tempMax: 16, tempMin: 6 },
    { icon: '400', textDay: '小雪', tempMax: 2, tempMin: -8 }
  ]

  const seed = location.toString().length
  const forecastData = []

  for (let i = 0; i < 7; i++) {
    const date = new Date()
    date.setDate(date.getDate() + i)

    const weatherIndex = (seed + i) % weatherTypes.length
    const weather = weatherTypes[weatherIndex]
    const tempVariation = ((seed + i) % 10) - 5

    forecastData.push({
      fxDate: date.toISOString().split('T')[0],
      tempMax: (weather.tempMax + tempVariation).toString(),
      tempMin: (weather.tempMin + tempVariation).toString(),
      textDay: weather.textDay,
      iconDay: weather.icon,
      textNight: weather.textDay,
      iconNight: weather.icon,
      windDirDay: '东南风',
      windSpeedDay: (8 + (i % 10)).toString(),
      humidity: (60 + (i % 30)).toString(),
      pressure: (1013 + (i % 20) - 10).toString(),
      vis: (15 + (i % 10)).toString()
    })
  }

  return forecastData
}

// 格式化更新时间
function formatUpdateTime(date) {
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`

  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`

  return date.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onBeforeUnmount(() => {
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', onScroll)
  }
  window.removeEventListener('keydown', onKeydown)
  window.removeEventListener('resize', resizeCanvas)
  stopFx()
})
</script>

<style scoped>
/* 基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.weather-app {
  min-height: 100vh;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  /* 默认深色基底，具体天气用 bg-* 类覆盖 */
  background: linear-gradient(135deg, #050509 0%, #0d0d12 100%);
}
/* 根背景跟随天气变化：确保“整屏都是天气” */
.weather-app.bg-sunny { background: linear-gradient(135deg, #060608, #0d0d12), radial-gradient(80rem 40rem at 20% 10%, rgba(255,224,150,.12), transparent 60%) no-repeat fixed; }
.weather-app.bg-windy { background: linear-gradient(135deg, #05070a, #0a0e14), radial-gradient(70rem 30rem at 60% 10%, rgba(120,190,255,.08), transparent 60%) no-repeat fixed; }
.weather-app.bg-storm { background: linear-gradient(135deg, #0a0a11, #0a0b12), radial-gradient(60rem 40rem at 50% 0%, rgba(255,255,255,.05), transparent 60%) no-repeat fixed; }
.weather-app.bg-blizzard { background: linear-gradient(135deg, #08090c, #0d0f13), radial-gradient(80rem 50rem at 50% 20%, rgba(255,255,255,.06), transparent 60%) no-repeat fixed; }

/* 全屏天气背景（最低层） */
.global-bg {
  position: fixed;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

/* 2D Canvas FX 层：用于体积光/风线/雨幕/雪（位于全屏背景之上，内容之下） */
.fx-canvas {
  position: fixed;
  inset: 0;
  z-index: 2;
  pointer-events: none;
  filter: saturate(112%) contrast(102%);
  will-change: transform;
  mix-blend-mode: screen; /* 与底层渐变/全局背景叠加，增强体积感 */
}

/* sunny 全屏背景 */
.sunny-global {
  background: radial-gradient(circle at 20% 10%, rgba(255,213,130,.08), transparent 40%),
              radial-gradient(circle at 80% 20%, rgba(255,255,255,.06), transparent 45%);
  mix-blend-mode: screen;
}
.global-sun {
  position: absolute;
  top: 8%;
  left: 12%;
  width: 140px;
  height: 140px;
  border-radius: 50%;
  background: radial-gradient(circle, #fff7b3 0%, #ffd15e 35%, rgba(255,209,94,.0) 70%);
  filter: blur(2px);
  animation: pulse 3s ease-in-out infinite;
}
.global-glow {
  position: absolute;
  top: 8%;
  left: 12%;
  width: 380px;
  height: 380px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,210,120,.22), rgba(255,210,120,0) 70%);
  filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}
.global-dust {
  position: absolute;
  top: calc(10% + var(--delay) * 4%);
  left: calc(20% + var(--delay) * 6%);
  width: 6px; height: 6px;
  border-radius: 50%;
  background: rgba(255,255,255,.25);
  animation: sparkle 3.2s ease-in-out infinite;
  animation-delay: var(--delay);
}

/* windy 全屏背景 */
.windy-global {
  background: radial-gradient(circle at 60% 10%, rgba(139,197,255,.06), transparent 40%);
}
.global-wind {
  position: absolute;
  left: -20%;
  right: -20%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,.35), transparent);
  top: 30%;
  animation: wind-flow 2.4s ease-in-out infinite;
  animation-delay: var(--delay);
  opacity: .8;
}
.global-cloud {
  position: absolute;
  background: rgba(255,255,255,.12);
  border-radius: 60px;
  filter: blur(2px);
  animation: cloud-drift 12s ease-in-out infinite;
}
.gc-1 { width: 140px; height: 70px; top: 18%; left: 65%; }
.gc-2 { width: 100px; height: 50px; top: 38%; left: 78%; animation-delay: 2s; }

/* storm 全屏背景 */
.storm-global {
  background: radial-gradient(circle at 50% 0%, rgba(255,255,255,.03), transparent 60%),
              linear-gradient(180deg, rgba(20,20,30,.4), rgba(10,10,16,.8));
}
.global-rain {
  position: absolute;
  top: -10%;
  width: 2px;
  height: 18vh;
  background: linear-gradient(to bottom, rgba(170,210,255,.7), rgba(120,160,255,0));
  transform: translateY(-20px);
  animation: global-rain-fall 1.1s linear infinite;
  animation-delay: var(--delay);
  opacity: .75;
}
@keyframes global-rain-fall {
  0% { transform: translateY(-20px); opacity: .8; }
  100% { transform: translateY(120vh); opacity: 0; }
}
.global-flash {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 70% 10%, rgba(255,255,255,.2), rgba(255,255,255,0) 40%);
  mix-blend-mode: screen;
  opacity: 0;
  animation: flash 5s ease-in-out infinite;
}
.global-mist {
  position: absolute;
  bottom: -10%;
  left: 0; right: 0;
  height: 40vh;
  background: linear-gradient(180deg, rgba(180,195,220,.08), rgba(180,195,220,0));
  filter: blur(6px);
}

/* snow 全屏背景 */
.snow-global {
  background: radial-gradient(circle at 50% 20%, rgba(255,255,255,.06), transparent 50%);
}
.global-snow {
  position: absolute;
  top: -5%;
  width: 6px; height: 6px;
  border-radius: 50%;
  background: rgba(255,255,255,.85);
  box-shadow: 0 0 8px rgba(255,255,255,.5);
  animation: global-snow-fall 6s linear infinite;
  animation-delay: var(--delay);
}
@keyframes global-snow-fall {
  0% { transform: translateY(-10px) translateX(0) rotate(0deg); opacity: .95; }
  100% { transform: translateY(110vh) translateX(-20px) rotate(240deg); opacity: 0; }
}
.global-haze {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 50% 80%, rgba(255,255,255,.08), rgba(255,255,255,0) 60%);
  filter: blur(4px);
}
.sr-only {
  position: absolute !important;
  width: 1px; height: 1px;
  padding: 0; margin: -1px;
  overflow: hidden; clip: rect(0,0,0,0);
  white-space: nowrap; border: 0;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  height: 80px;
  backdrop-filter: saturate(180%) blur(20px);
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 3; /* 位于 Canvas FX 之上 */
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.title {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff, #a0a0a0);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
}

.spacer {
  width: 120px;
}

/* 卡片容器 */
.cards-container {
  position: relative;
  z-index: 3; /* 位于 Canvas FX 之上 */
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: minmax(360px, 1fr);
  gap: 24px;
  padding: 32px 24px;
  overflow-x: auto;
  scroll-snap-type: x proximity; /* 使用 proximity 以便自定义磁吸 */
  scroll-padding-inline: 24px;
  flex: 1;
  align-items: center;
  scroll-behavior: smooth;
}

.cards-container::-webkit-scrollbar {
  height: 6px;
}

.cards-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.cards-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

/* 天气卡片 */
.weather-card {
  position: relative;
  height: 520px;
  border-radius: 28px;
  overflow: hidden;
  scroll-snap-align: center;
  cursor: pointer;
  transition: transform .5s cubic-bezier(.2,.7,.2,1), box-shadow .4s ease, opacity .4s ease;
  transform: scale(0.95) translateY(10px);
  opacity: 0.85;
  backdrop-filter: saturate(180%) blur(12px);
  border: 1px solid rgba(255,255,255,0.12);
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,0.15),
    0 20px 60px rgba(0,0,0,0.35);
}

.weather-card.active {
  transform: translateY(0) scale(1.02);
  opacity: 1;
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,0.2),
    0 26px 80px rgba(0,0,0,0.45);
}

/* 吸附到位时轻微回弹 */
.weather-card.snap-bounce {
  animation: snap-bounce 160ms cubic-bezier(0.2, 0.8, 0.2, 1);
}
@keyframes snap-bounce {
  0% { transform: scale(1.01) translateY(0); }
  100% { transform: scale(1) translateY(0); }
}

.weather-card.tap {
  transform: scale(0.98);
}

.card-bg {
  position: absolute;
  inset: 0;
  z-index: 0;
}

.card-content {
  position: relative;
  z-index: 2;
  height: 100%;
  padding: 28px 28px 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 顶部信息区域的分隔与对齐 */
.card-content::after {
  content: '';
  position: absolute;
  left: 28px; right: 28px;
  top: 108px;
  height: 1px;
  background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.16), rgba(255,255,255,0));
  pointer-events: none;
}
.card-bg [aria-hidden="true"] {
  pointer-events: none;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 32px;
}

.icon-section {
  flex-shrink: 0;
}

.weather-icon {
  width: 64px;
  height: 64px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(0 6px 18px rgba(0,0,0,.25));
}

.temp-section {
  flex: 1;
}

.temperature {
  font-size: 58px;
  font-weight: 900;
  line-height: 1;
  margin-bottom: 6px;
  letter-spacing: -0.5px;
  text-shadow:
    0 6px 24px rgba(0,0,0,.35),
    0 1px 0 rgba(255,255,255,.12);
}

.weather-type {
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  color: rgba(255,255,255,.92);
  padding: 4px 10px;
  border-radius: 12px;
  background: rgba(255,255,255,.12);
  border: 1px solid rgba(255,255,255,.22);
  backdrop-filter: blur(8px);
  margin-bottom: 6px;
}

.location {
  font-size: 13px;
  color: rgba(255,255,255,.85);
  font-weight: 600;
  letter-spacing: .2px;
  opacity: .9;
}

.details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 14px 16px;
  margin-top: 6px;
}

.detail {
  position: relative;
  padding: 16px 18px;
  border-radius: 16px;
  background: linear-gradient(180deg, rgba(255,255,255,.10), rgba(255,255,255,.06));
  border: 1px solid rgba(255,255,255,.18);
  backdrop-filter: blur(12px);
  display: flex;
  flex-direction: column;
  gap: 6px;
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,.25),
    0 10px 30px rgba(0,0,0,.18);
  overflow: hidden;
}
.detail::after {
  content: '';
  position: absolute;
  right: 0; top: 0;
  width: 54px; height: 20px;
  border-bottom-left-radius: 999px;
  background: linear-gradient(90deg, rgba(255,255,255,.24), rgba(255,255,255,0));
  opacity: .55;
  filter: blur(.3px);
  pointer-events: none;
}

.label {
  font-size: 12px;
  color: rgba(255,255,255,.72);
  font-weight: 600;
  letter-spacing: .2px;
}

.value {
  font-size: 18px;
  color: #ffffff;
  font-weight: 800;
  letter-spacing: .2px;
  text-shadow: 0 2px 10px rgba(0,0,0,.25);
}

/* 晴天样式 */
.sunny-bg {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.sun-rays {
  position: absolute;
  inset: -20px;
  animation: rotate 20s linear infinite;
}

.ray {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120px;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform-origin: 0 50%;
  transform: translate(0, -50%) rotate(var(--angle));
  border-radius: 2px;
}

.particles {
  position: absolute;
  inset: 0;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: sparkle 3s ease-in-out infinite;
  animation-delay: var(--delay);
}

.particle:nth-child(1) { top: 20%; left: 20%; }
.particle:nth-child(2) { top: 30%; right: 25%; }
.particle:nth-child(3) { top: 60%; left: 30%; }
.particle:nth-child(4) { bottom: 30%; right: 20%; }
.particle:nth-child(5) { top: 45%; left: 65%; }
.particle:nth-child(6) { bottom: 25%; left: 45%; }

.sun-icon {
  animation: float 4s ease-in-out infinite;
}

.sun-core {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  background: radial-gradient(circle at 40% 35%, #ffffff 0%, #fff6a0 35%, #ffd35a 70%, #ffc14a 100%);
  box-shadow:
    0 0 24px rgba(255, 214, 110, 0.7),
    inset 0 2px 6px rgba(255,255,255,0.6);
}

.sun-glow {
  position: absolute;
  inset: -14px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 235, 130, 0.45) 0%, rgba(255, 235, 130, 0.0) 70%);
  animation: pulse 2.4s ease-in-out infinite;
}

/* 实时天气样式 */
.realtime-bg {
  background: linear-gradient(135deg, #667eea, #764ba2);
  position: relative;
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  z-index: 2;
}

.loading-spinner-large {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
}

.weather-effects {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.weather-particles {
  position: absolute;
  inset: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
  animation-delay: var(--delay);
  top: 20%;
  left: 20%;
}

.realtime-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.weather-emoji {
  font-size: 48px;
  animation: bounce 2s ease-in-out infinite;
}

.error-icon {
  font-size: 48px;
  opacity: 0.7;
}

.placeholder-icon {
  font-size: 48px;
  opacity: 0.6;
  animation: pulse 2s ease-in-out infinite;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.detail-grid .detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 12px;
}

.detail-grid .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.detail-grid .value {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
}

.error-message {
  color: #ff6b6b;
  font-size: 13px;
  text-align: center;
  padding: 12px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 107, 107, 0.2);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.error-text {
  flex: 1;
}

.error-close-btn {
  background: none;
  border: none;
  color: #ff6b6b;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.error-close-btn:hover {
  background: rgba(255, 107, 107, 0.2);
  transform: scale(1.1);
}

.placeholder-message {
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
  text-align: center;
  padding: 12px;
  font-style: italic;
}

.update-time {
  position: absolute;
  bottom: 12px;
  right: 12px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(0, 0, 0, 0.3);
  padding: 4px 8px;
  border-radius: 4px;
}

/* 大风样式 */
.windy-bg {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.wind-lines {
  position: absolute;
  inset: 0;
}

.wind-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  border-radius: 1px;
  animation: wind-flow 2s ease-in-out infinite;
  animation-delay: var(--delay);
  top: calc(25% + var(--delay) * 50px);
  left: -20%;
  right: -20%;
}

.clouds {
  position: absolute;
  inset: 0;
}

.cloud {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  animation: cloud-drift 6s ease-in-out infinite;
}

.cloud-1 {
  width: 60px;
  height: 30px;
  top: 25%;
  left: 20%;
}

.cloud-2 {
  width: 45px;
  height: 22px;
  top: 55%;
  right: 25%;
  animation-delay: 2s;
}

/* 大风图标容器 */
.wind-container {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 风旋效果 - 多层旋转 */
.wind-spiral {
  position: absolute;
  border-radius: 50%;
  border-style: solid;
  animation: spin linear infinite;
}

.wind-spiral.outer {
  width: 50px;
  height: 50px;
  border-width: 3px;
  border-color: rgba(255, 255, 255, 0.6) transparent rgba(255, 255, 255, 0.3) transparent;
  animation-duration: 2s;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
}

.wind-spiral.middle {
  width: 35px;
  height: 35px;
  border-width: 2px;
  border-color: transparent rgba(255, 255, 255, 0.8) transparent rgba(255, 255, 255, 0.4);
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.wind-spiral.inner {
  width: 20px;
  height: 20px;
  border-width: 2px;
  border-color: rgba(255, 255, 255, 0.9) transparent rgba(255, 255, 255, 0.6) transparent;
  animation-duration: 1s;
}

/* 图标内风线效果 */
.wind-container .wind-line {
  position: absolute;
  width: 25px;
  height: 2px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), transparent);
  border-radius: 1px;
  transform-origin: left center;
  transform: rotate(var(--angle));
  animation: wind-pulse 2s ease-in-out infinite;
  animation-delay: var(--delay);
}

/* 中心点 */
.wind-center {
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.4));
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* 暴雨样式 */
.storm-bg {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.rain-drops {
  position: absolute;
  inset: 0;
}

.drop {
  position: absolute;
  width: 2px;
  height: 12px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(79, 172, 254, 0.6));
  border-radius: 0 0 2px 2px;
  animation: rain-fall 1s linear infinite;
  animation-delay: var(--delay);
  top: 50%;
  left: calc(20% + var(--offset));
}

.lightning {
  position: absolute;
  inset: 0;
  background: radial-gradient(120px 60px at 70% 10%, rgba(255,255,255,.14), rgba(255,255,255,0));
  opacity: 0;
  animation: flash 4s ease-in-out infinite;
  mix-blend-mode: screen;
}

/* 暴雨图标容器 */
.rain-container {
  position: relative;
  width: 60px;
  height: 70px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

/* 云朵效果 */
.rain-cloud {
  position: absolute;
  border-radius: 20px;
}

.rain-cloud.main {
  width: 50px;
  height: 28px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(220, 220, 220, 0.9));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 2;
}

.rain-cloud.main::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(200, 200, 200, 0.8));
  border-radius: 50%;
  top: -8px;
  left: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.rain-cloud.main::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.85), rgba(210, 210, 210, 0.8));
  border-radius: 50%;
  top: -6px;
  right: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rain-cloud.shadow {
  width: 48px;
  height: 26px;
  background: linear-gradient(135deg, rgba(180, 180, 180, 0.4), rgba(150, 150, 150, 0.3));
  top: 2px;
  left: 1px;
  z-index: 1;
}

/* 闪电效果 */
.lightning {
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 3px;
  height: 20px;
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 100, 0.8) 30%,
    rgba(255, 255, 255, 0.7) 60%,
    transparent 100%);
  border-radius: 1px;
  opacity: 0;
  animation: lightning-flash 3s ease-in-out infinite;
  z-index: 3;
}

.lightning::before {
  content: '';
  position: absolute;
  top: 8px;
  left: -2px;
  width: 0;
  height: 0;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 6px solid rgba(255, 255, 100, 0.8);
}

/* 雨滴流 */
.rain-streams {
  position: absolute;
  top: 25px;
  left: 50%;
  transform: translateX(-50%);
  width: 55px;
  height: 40px;
}

.stream {
  position: absolute;
  width: 2px;
  height: 18px;
  background: linear-gradient(to bottom,
    rgba(79, 172, 254, 0.9) 0%,
    rgba(79, 172, 254, 0.6) 50%,
    transparent 100%);
  border-radius: 0 0 2px 2px;
  animation: heavy-rain-drop 0.5s linear infinite;
  animation-delay: var(--delay);
  left: calc(10% + var(--offset));
  box-shadow: 0 0 3px rgba(79, 172, 254, 0.3);
}

.stream[style*="heavy"] {
  width: 3px;
  height: 22px;
  background: linear-gradient(to bottom,
    rgba(79, 172, 254, 1) 0%,
    rgba(79, 172, 254, 0.8) 40%,
    rgba(79, 172, 254, 0.4) 80%,
    transparent 100%);
  animation-duration: 0.4s;
}

/* 水花效果 */
.rain-splash-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 8px;
}

.rain-splash {
  position: absolute;
  bottom: 0;
  width: 8px;
  height: 3px;
  left: var(--position);
  opacity: 0;
  animation: splash-effect 1.2s ease-out infinite;
  animation-delay: var(--delay);
}

.rain-splash::before,
.rain-splash::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 2px;
  background: rgba(79, 172, 254, 0.6);
  border-radius: 50%;
  bottom: 0;
}

.rain-splash::before {
  left: -2px;
  animation: splash-left 1.2s ease-out infinite;
  animation-delay: var(--delay);
}

.rain-splash::after {
  right: -2px;
  animation: splash-right 1.2s ease-out infinite;
  animation-delay: var(--delay);
}

/* 暴雪样式 */
.snow-bg {
  background: linear-gradient(160deg, #ede9e3 0%, #dcdad6 50%, #d2d1ce 100%);
}

.snowflakes {
  position: absolute;
  inset: 0;
}

.snowflake {
  position: absolute;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  animation: snow-fall 3s linear infinite;
  animation-delay: var(--delay);
  top: 45%;
  left: calc(15% + var(--offset));
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

.snow-crystal {
  position: relative;
  width: 56px;
  height: 56px;
  animation: crystal-rotate 8s linear infinite;
  filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.4));
}

.crystal-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 230, 255, 0.9) 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow:
    0 0 8px rgba(255, 255, 255, 0.8),
    inset 0 1px 2px rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.crystal-arm {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: 0 50%;
  transform: translate(0, -50%) rotate(var(--angle));
  border-radius: 1px;
}

.crystal-arm.main {
  width: 24px;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(200, 230, 255, 0.8) 70%,
    transparent 100%);
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
}

.crystal-arm.secondary {
  width: 16px;
  height: 1.5px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(200, 230, 255, 0.5) 60%,
    transparent 100%);
}

.crystal-branch {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: 0 50%;
  transform: translate(0, -50%) rotate(var(--angle));
}

.branch-left,
.branch-right {
  position: absolute;
  width: 8px;
  height: 1px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.6), transparent);
  transform-origin: 0 50%;
}

.branch-left {
  top: -3px;
  left: 12px;
  transform: rotate(-30deg);
}

.branch-right {
  top: 3px;
  left: 12px;
  transform: rotate(30deg);
}

.crystal-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform-origin: 0 50%;
  transform: translate(-1px, -1px) rotate(var(--angle)) translateX(var(--distance));
  box-shadow: 0 0 3px rgba(255, 255, 255, 0.6);
}

.crystal-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: crystal-glow 3s ease-in-out infinite;
  z-index: -1;
}

/* 底部导航 */
.navigation {
  position: relative;
  z-index: 3; /* 位于 Canvas FX 之上 */
  padding: 24px;
  backdrop-filter: saturate(180%) blur(20px);
  background: rgba(0, 0, 0, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-dots {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.nav-dot {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 18px;
  overflow: hidden;
}
.nav-dot .dot-icon {
  position: relative;
  z-index: 2;
  transition: transform .25s ease, opacity .25s ease;
}
.nav-dot .dot-ripple {
  position: absolute;
  inset: 0;
  border-radius: 24px;
  background: radial-gradient(closest-side, rgba(255,255,255,.35), rgba(255,255,255,0) 70%);
  opacity: 0;
  transform: scale(0.6);
  z-index: 1;
}
.nav-dot.active .dot-ripple {
  animation: dot-ripple 600ms ease-out;
}
.nav-dot.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.25);
}
.nav-dot:not(.active) .dot-icon {
  opacity: .85;
}
@keyframes dot-ripple {
  0% { opacity: .0; transform: scale(0.6); }
  20% { opacity: .5; transform: scale(0.9); }
  100% { opacity: 0; transform: scale(1.3); }
}

.nav-dot:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.nav-dot.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.25);
}

/* 动画关键帧 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes sparkle {
  0%, 100% { opacity: 0.4; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-6px); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

@keyframes wind-flow {
  0% { transform: translateX(-100%); opacity: 0; }
  20% { opacity: 0.8; }
  80% { opacity: 0.8; }
  100% { transform: translateX(200%); opacity: 0; }
}

@keyframes cloud-drift {
  0%, 100% { transform: translateX(0) translateY(0); }
  33% { transform: translateX(10px) translateY(-3px); }
  66% { transform: translateX(-5px) translateY(3px); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes rain-fall {
  0% { transform: translateY(0); opacity: 0.8; }
  100% { transform: translateY(150px); opacity: 0; }
}

@keyframes flash {
  0%, 90%, 100% { opacity: 0; }
  91%, 93%, 95% { opacity: 0.6; }
  92%, 94% { opacity: 0.2; }
}

@keyframes stream-drop {
  0% { transform: translateY(0); opacity: 0.9; }
  100% { transform: translateY(25px); opacity: 0; }
}

@keyframes snow-fall {
  0% { transform: translateY(0) rotate(0deg); opacity: 0.9; }
  100% { transform: translateY(180px) rotate(180deg); opacity: 0; }
}

@keyframes crystal-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes crystal-glow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 位置控制面板样式 */
.location-control {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 280px;
  max-width: 320px;
}

.location-info {
  margin-bottom: 12px;
}

.current-location {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.location-icon {
  font-size: 16px;
  opacity: 0.8;
}

.location-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.location-error {
  color: #ff6b6b;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 4px;
  padding: 6px 8px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(255, 107, 107, 0.2);
}

.location-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.location-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 36px;
}

.location-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.location-btn:active:not(:disabled) {
  transform: translateY(0);
}

.location-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.location-btn.loading {
  pointer-events: none;
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.manual-location {
  display: flex;
  gap: 6px;
}

.city-input {
  flex: 1;
  padding: 8px 10px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  outline: none;
  transition: all 0.2s ease;
}

.city-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.city-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.set-location {
  min-width: 36px;
  padding: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-container {
    grid-auto-columns: minmax(300px, 1fr);
    gap: 20px;
    padding: 24px 16px;
  }
  
  .weather-card {
    height: 450px;
  }
  
  .temperature {
    font-size: 48px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 16px 20px;
    height: 70px;
  }
  
  .cards-container {
    grid-auto-columns: minmax(280px, 1fr);
    gap: 16px;
    padding: 20px 12px;
  }
  
  .weather-card {
    height: 400px;
  }
  
  .card-content {
    padding: 24px;
  }
  
  .temperature {
    font-size: 42px;
  }
  
  .weather-type {
    font-size: 16px;
  }

  /* 移动端位置控制面板 */
  .location-control {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
    padding: 12px;
  }

  .location-actions {
    flex-direction: row;
  }

  .manual-location {
    flex: 1;
  }

  .location-btn.get-location {
    min-width: 80px;
  }
}

/* 新增动画关键帧 */
@keyframes wind-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: rotate(var(--angle)) scaleX(0.8);
  }
  50% {
    opacity: 0.8;
    transform: rotate(var(--angle)) scaleX(1.2);
  }
}

@keyframes lightning-flash {
  0%, 85%, 100% { opacity: 0; }
  86%, 88%, 90% { opacity: 0.9; }
  87%, 89% { opacity: 0.3; }
}

@keyframes heavy-rain-drop {
  0% {
    transform: translateY(-5px);
    opacity: 0.9;
  }
  100% {
    transform: translateY(35px);
    opacity: 0;
  }
}

@keyframes splash-effect {
  0% { opacity: 0; transform: scale(0.5); }
  20% { opacity: 0.8; transform: scale(1); }
  100% { opacity: 0; transform: scale(1.5); }
}

@keyframes splash-left {
  0% { transform: translateX(0) translateY(0); opacity: 0; }
  20% { opacity: 0.6; }
  100% { transform: translateX(-6px) translateY(-3px); opacity: 0; }
}

@keyframes splash-right {
  0% { transform: translateX(0) translateY(0); opacity: 0; }
  20% { opacity: 0.6; }
  100% { transform: translateX(6px) translateY(-3px); opacity: 0; }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-10px) translateX(5px);
    opacity: 0.8;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

</style>