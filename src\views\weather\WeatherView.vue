<template>
  <div class="weather-app" :class="`bg-${activeWeather}`">
    <!-- 顶部导航 -->
    <header class="header">
      <button class="back-btn" @click="$router.back()">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回
      </button>
      <h1 class="title">天气</h1>
      <div class="spacer"></div>
    </header>

    <!-- 统一的全屏天气背景，固定在视口，置于内容之下 -->
    <div v-if="activeWeather === 'sunny'" class="global-bg sunny-global" aria-hidden="true">
      <div class="global-sun"></div>
      <div class="global-glow"></div>
      <div class="global-dust" v-for="n in 10" :key="'s'+n" :style="{ '--delay': n * 0.35 + 's' }"></div>
    </div>
    <div v-else-if="activeWeather === 'windy'" class="global-bg windy-global" aria-hidden="true">
      <div class="global-wind" v-for="n in 8" :key="'w'+n" :style="{ '--delay': n * 0.25 + 's', top: (10 + n*8) + '%' }"></div>
      <div class="global-cloud gc-1"></div>
      <div class="global-cloud gc-2"></div>
    </div>
    <div v-else-if="activeWeather === 'storm'" class="global-bg storm-global" aria-hidden="true">
      <div class="global-rain" v-for="n in 60" :key="'r'+n" :style="{ '--delay': (n%10)*0.1 + 's', left: (n*1.5)%100 + '%' }"></div>
      <div class="global-flash"></div>
      <div class="global-mist"></div>
    </div>
    <div v-else-if="activeWeather === 'blizzard'" class="global-bg snow-global" aria-hidden="true">
      <div class="global-snow" v-for="n in 40" :key="'sn'+n" :style="{ '--delay': (n%8)*0.2 + 's', left: (n*2.3)%100 + '%' }"></div>
      <div class="global-haze"></div>
    </div>

    <!-- 体积光/雨幕/雪场景 Canvas（固定在视口，作为全屏背景的动态层） -->
    <canvas ref="fxCanvas" class="fx-canvas" aria-hidden="true"></canvas>

    <!-- 天气卡片容器 -->
    <main class="cards-container" role="region" aria-label="天气卡片列表">
      <!-- 晴天卡片 -->
      <div class="weather-card sunny"
           :class="{ active: activeWeather === 'sunny' }"
           role="group"
           aria-roledescription="天气卡片"
           aria-label="晴天，北京，温度 {{ temps.sunny }} 度"
           tabindex="0"
           @mouseenter="setActive('sunny')"
           @click="selectWeather('sunny')"
           @keydown.enter.prevent="scrollToWeather('sunny')"
           @keydown.space.prevent="scrollToWeather('sunny')">
        
        <!-- 背景动画 -->
        <div class="card-bg sunny-bg">
          <div class="sun-rays">
            <div v-for="n in 8" :key="n" class="ray" :style="{ '--angle': n * 45 + 'deg' }"></div>
          </div>
          <div class="particles">
            <div v-for="n in 6" :key="n" class="particle" :style="{ '--delay': n * 0.5 + 's' }"></div>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="weather-info">
            <div class="icon-section">
              <div class="weather-icon sun-icon">
                <div class="sun-core"></div>
                <div class="sun-glow"></div>
              </div>
            </div>
            <div class="temp-section">
              <div class="temperature">{{ temps.sunny }}°</div>
              <div class="weather-type">晴天</div>
              <div class="location">北京</div>
            </div>
          </div>
          
          <div class="details">
            <div class="detail">
              <span class="label">体感</span>
              <span class="value">{{ temps.sunny - 1 }}°</span>
            </div>
            <div class="detail">
              <span class="label">湿度</span>
              <span class="value">35%</span>
            </div>
            <div class="detail">
              <span class="label">风速</span>
              <span class="value">3 m/s</span>
            </div>
            <div class="detail">
              <span class="label">紫外线</span>
              <span class="value">强</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 大风卡片 -->
      <div class="weather-card windy"
           :class="{ active: activeWeather === 'windy' }"
           role="group"
           aria-roledescription="天气卡片"
           aria-label="大风，北京，温度 {{ temps.windy }} 度"
           tabindex="0"
           @mouseenter="setActive('windy')"
           @click="selectWeather('windy')"
           @keydown.enter.prevent="scrollToWeather('windy')"
           @keydown.space.prevent="scrollToWeather('windy')">
        
        <div class="card-bg windy-bg">
          <div class="wind-lines">
            <div v-for="n in 5" :key="n" class="wind-line" :style="{ '--delay': n * 0.3 + 's' }"></div>
          </div>
          <div class="clouds">
            <div class="cloud cloud-1"></div>
            <div class="cloud cloud-2"></div>
          </div>
        </div>

        <div class="card-content">
          <div class="weather-info">
            <div class="icon-section">
              <div class="weather-icon wind-icon">
                <div class="wind-spiral"></div>
              </div>
            </div>
            <div class="temp-section">
              <div class="temperature">{{ temps.windy }}°</div>
              <div class="weather-type">大风</div>
              <div class="location">北京</div>
            </div>
          </div>
          
          <div class="details">
            <div class="detail">
              <span class="label">阵风</span>
              <span class="value">18 m/s</span>
            </div>
            <div class="detail">
              <span class="label">湿度</span>
              <span class="value">40%</span>
            </div>
            <div class="detail">
              <span class="label">能见度</span>
              <span class="value">良好</span>
            </div>
            <div class="detail">
              <span class="label">风向</span>
              <span class="value">西北</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 暴雨卡片 -->
      <div class="weather-card storm"
           :class="{ active: activeWeather === 'storm' }"
           role="group"
           aria-roledescription="天气卡片"
           aria-label="暴雨，北京，温度 {{ temps.storm }} 度"
           tabindex="0"
           @mouseenter="setActive('storm')"
           @click="selectWeather('storm')"
           @keydown.enter.prevent="scrollToWeather('storm')"
           @keydown.space.prevent="scrollToWeather('storm')">
        
        <div class="card-bg storm-bg">
          <div class="rain-drops">
            <div v-for="n in 15" :key="n" class="drop" :style="{ '--delay': n * 0.1 + 's', '--offset': n * 8 + 'px' }"></div>
          </div>
          <div class="lightning"></div>
        </div>

        <div class="card-content">
          <div class="weather-info">
            <div class="icon-section">
              <div class="weather-icon rain-icon">
                <div class="rain-cloud"></div>
                <div class="rain-streams">
                  <div v-for="n in 6" :key="n" class="stream" :style="{ '--delay': n * 0.12 + 's' }"></div>
                </div>
              </div>
            </div>
            <div class="temp-section">
              <div class="temperature">{{ temps.storm }}°</div>
              <div class="weather-type">暴雨</div>
              <div class="location">北京</div>
            </div>
          </div>
          
          <div class="details">
            <div class="detail">
              <span class="label">降水量</span>
              <span class="value">45 mm/h</span>
            </div>
            <div class="detail">
              <span class="label">湿度</span>
              <span class="value">92%</span>
            </div>
            <div class="detail">
              <span class="label">雷暴</span>
              <span class="value">预警</span>
            </div>
            <div class="detail">
              <span class="label">能见度</span>
              <span class="value">差</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 暴雪卡片 -->
      <div class="weather-card snow"
           :class="{ active: activeWeather === 'blizzard' }"
           role="group"
           aria-roledescription="天气卡片"
           aria-label="暴雪，北京，温度 {{ temps.blizzard }} 度"
           tabindex="0"
           @mouseenter="setActive('blizzard')"
           @click="selectWeather('blizzard')"
           @keydown.enter.prevent="scrollToWeather('blizzard')"
           @keydown.space.prevent="scrollToWeather('blizzard')">
        
        <div class="card-bg snow-bg">
          <div class="snowflakes">
            <div v-for="n in 20" :key="n" class="snowflake" :style="{ '--delay': n * 0.2 + 's', '--offset': n * 6 + 'px' }">❄</div>
          </div>
        </div>

        <div class="card-content">
          <div class="weather-info">
            <div class="icon-section">
              <div class="weather-icon snow-icon">
                <div class="snow-crystal">
                  <div class="crystal-center"></div>
                  <!-- 主要臂膀 -->
                  <div v-for="n in 6" :key="n" class="crystal-arm main" :style="{ '--angle': n * 60 + 'deg' }"></div>
                  <!-- 次要臂膀 -->
                  <div v-for="n in 6" :key="'s'+n" class="crystal-arm secondary" :style="{ '--angle': n * 60 + 30 + 'deg' }"></div>
                  <!-- 分支结构 -->
                  <div v-for="n in 6" :key="'b'+n" class="crystal-branch" :style="{ '--angle': n * 60 + 'deg' }">
                    <div class="branch-left"></div>
                    <div class="branch-right"></div>
                  </div>
                  <!-- 装饰性结晶点 -->
                  <div v-for="n in 12" :key="'d'+n" class="crystal-dot" :style="{ '--angle': n * 30 + 'deg', '--distance': (n % 2 === 0 ? '15px' : '25px') }"></div>
                  <!-- 外层光环 -->
                  <div class="crystal-glow"></div>
                </div>
              </div>
            </div>
            <div class="temp-section">
              <div class="temperature">{{ temps.blizzard }}°</div>
              <div class="weather-type">暴雪</div>
              <div class="location">北京</div>
            </div>
          </div>
          
          <div class="details">
            <div class="detail">
              <span class="label">积雪</span>
              <span class="value">12 cm</span>
            </div>
            <div class="detail">
              <span class="label">体感</span>
              <span class="value">{{ temps.blizzard - 5 }}°</span>
            </div>
            <div class="detail">
              <span class="label">道路</span>
              <span class="value">结冰</span>
            </div>
            <div class="detail">
              <span class="label">能见度</span>
              <span class="value">极差</span>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 底部导航 -->
    <footer class="navigation">
      <div class="nav-dots" role="tablist" aria-label="天气类型导航">
        <button class="nav-dot"
                :class="{ active: activeWeather === 'sunny' }"
                @click="scrollToWeather('sunny')"
                role="tab"
                :aria-selected="activeWeather === 'sunny'"
                :tabindex="activeWeather === 'sunny' ? 0 : -1"
                aria-label="切换到晴天">
          <span class="dot-ripple" aria-hidden="true"></span>
          <span class="dot-icon">☀️</span>
        </button>
        <button class="nav-dot"
                :class="{ active: activeWeather === 'windy' }"
                @click="scrollToWeather('windy')"
                role="tab"
                :aria-selected="activeWeather === 'windy'"
                :tabindex="activeWeather === 'windy' ? 0 : -1"
                aria-label="切换到大风">
          <span class="dot-ripple" aria-hidden="true"></span>
          <span class="dot-icon">💨</span>
        </button>
        <button class="nav-dot"
                :class="{ active: activeWeather === 'storm' }"
                @click="scrollToWeather('storm')"
                role="tab"
                :aria-selected="activeWeather === 'storm'"
                :tabindex="activeWeather === 'storm' ? 0 : -1"
                aria-label="切换到暴雨">
          <span class="dot-ripple" aria-hidden="true"></span>
          <span class="dot-icon">⛈️</span>
        </button>
        <button class="nav-dot"
                :class="{ active: activeWeather === 'blizzard' }"
                @click="scrollToWeather('blizzard')"
                role="tab"
                :aria-selected="activeWeather === 'blizzard'"
                :tabindex="activeWeather === 'blizzard' ? 0 : -1"
                aria-label="切换到暴雪">
          <span class="dot-ripple" aria-hidden="true"></span>
          <span class="dot-icon">❄️</span>
        </button>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'

const activeWeather = ref('sunny')
const temps = reactive({
  sunny: 28,
  windy: 20,
  storm: 23,
  blizzard: -6
})

const containerRef = ref(null)
const fxCanvas = ref(null)

function setActive(weather) {
  activeWeather.value = weather
}

function selectWeather(weather) {
  activeWeather.value = weather
  const card = document.querySelector(`.weather-card.${weather}`)
  if (card) {
    card.classList.add('tap')
    setTimeout(() => card.classList.remove('tap'), 200)
  }
}

function scrollToWeather(weather) {
  activeWeather.value = weather
  const card = document.querySelector(`.weather-card.${weather}`)
  if (card) {
    card.scrollIntoView({
      behavior: 'smooth',
      inline: 'center',
      block: 'nearest'
    })
    // 回弹动画：到位后轻微放大再回到1
    setTimeout(() => {
      card.classList.add('snap-bounce')
      setTimeout(() => card.classList.remove('snap-bounce'), 160)
    }, 420)
  }
}

// 计算最接近中心的卡片
function snapToNearest() {
  const el = containerRef.value
  if (!el) return
  const rect = el.getBoundingClientRect()
  const centerX = rect.left + rect.width / 2

  const cards = Array.from(el.querySelectorAll('.weather-card'))
  if (!cards.length) return

  let nearest = null
  let minDist = Infinity
  cards.forEach(card => {
    const r = card.getBoundingClientRect()
    const cardCenter = r.left + r.width / 2
    const dist = Math.abs(cardCenter - centerX)
    if (dist < minDist) {
      minDist = dist
      nearest = card
    }
  })

  if (nearest) {
    // 平滑吸附
    nearest.scrollIntoView({
      behavior: 'smooth',
      inline: 'center',
      block: 'nearest'
    })
    // 设置激活态
    const classes = nearest.classList
    if (classes.contains('sunny')) setActive('sunny')
    else if (classes.contains('windy')) setActive('windy')
    else if (classes.contains('storm')) setActive('storm')
    else if (classes.contains('snow')) setActive('blizzard')

    // 卡片回弹动画
    nearest.classList.add('snap-bounce')
    setTimeout(() => nearest.classList.remove('snap-bounce'), 160)
  }
}

// 防抖工具
let scrollTimer = null
function onScroll() {
  if (scrollTimer) clearTimeout(scrollTimer)
  scrollTimer = setTimeout(snapToNearest, 120)
}

function onKeydown(e) {
  const order = ['sunny', 'windy', 'storm', 'blizzard']
  const idx = order.indexOf(activeWeather.value)
  if (e.key === 'ArrowRight') {
    const next = order[Math.min(order.length - 1, idx + 1)]
    if (next && next !== activeWeather.value) scrollToWeather(next)
  } else if (e.key === 'ArrowLeft') {
    const prev = order[Math.max(0, idx - 1)]
    if (prev && prev !== activeWeather.value) scrollToWeather(prev)
  } else if (e.key === 'Home') {
    scrollToWeather(order[0])
  } else if (e.key === 'End') {
    scrollToWeather(order[order.length - 1])
  }
}

/* ===== Canvas FX：体积光/风线/雨幕/雪 ===== */
let ctx = null
let rafId = 0
let particles = []
let lastT = 0
let dpi = 1

function resizeCanvas() {
  const cvs = fxCanvas.value
  if (!cvs) return
  const rect = { w: window.innerWidth, h: window.innerHeight }
  dpi = Math.min(window.devicePixelRatio || 1, 2)
  cvs.width = Math.floor(rect.w * dpi)
  cvs.height = Math.floor(rect.h * dpi)
  cvs.style.width = rect.w + 'px'
  cvs.style.height = rect.h + 'px'
  ctx = cvs.getContext('2d', { alpha: true })
  if (ctx) {
    ctx.setTransform(dpi, 0, 0, dpi, 0, 0)
  }
}

function createParticlesFor(weather) {
  particles = []
  const w = window.innerWidth
  const h = window.innerHeight
  const rand = (a, b) => a + Math.random() * (b - a)

  if (weather === 'sunny') {
    // 体积光微尘粒子
    for (let i = 0; i < 120; i++) {
      particles.push({
        type: 'dust',
        x: rand(0, w),
        y: rand(0, h),
        r: rand(0.6, 1.6),
        a: rand(0.12, 0.35),
        vx: rand(-0.03, 0.03),
        vy: rand(-0.03, 0.03)
      })
    }
  } else if (weather === 'windy') {
    // 远景风线 + 漂浮尘
    for (let i = 0; i < 40; i++) {
      particles.push({
        type: 'wind',
        y: rand(0, h * 0.7),
        x: rand(-w, w),
        len: rand(80, 200),
        a: rand(0.15, 0.35),
        v: rand(120, 220) // px/s
      })
    }
    for (let i = 0; i < 80; i++) {
      particles.push({
        type: 'speck',
        x: rand(0, w), y: rand(0, h),
        r: rand(0.5, 1.2), a: rand(0.08, 0.2),
        vx: rand(10, 40), vy: rand(-10, 10) // 被风带动
      })
    }
  } else if (weather === 'storm') {
    // 全屏雨幕（不同速度/粗细）
    for (let i = 0; i < 220; i++) {
      const speed = rand(500, 1200) // px/s
      particles.push({
        type: 'rain',
        x: rand(0, w), y: rand(-h, h),
        len: rand(10, 32),
        a: rand(0.35, 0.75),
        v: speed,
        drift: rand(-80, -20)
      })
    }
  } else if (weather === 'blizzard') {
    // 多层雪（近/中/远不同大小与速度）+ 近景轻雾
    for (let i = 0; i < 160; i++) {
      const layer = Math.random()
      particles.push({
        type: 'snow',
        x: rand(0, w), y: rand(-h, h),
        r: layer < 0.2 ? rand(2.2, 3.2) : layer < 0.6 ? rand(1.4, 2.2) : rand(0.8, 1.4),
        a: layer < 0.2 ? 0.9 : layer < 0.6 ? 0.75 : 0.6,
        vy: layer < 0.2 ? rand(80, 120) : layer < 0.6 ? rand(50, 80) : rand(30, 50),
        vx: rand(-20, 20),
        sway: rand(0.8, 2.2),
        phi: rand(0, Math.PI * 2)
      })
    }
  }
}

function renderSunny(dt) {
  const w = window.innerWidth, h = window.innerHeight
  // 体积光：左上角暖色锥形渐变
  const gx = 0.18 * w, gy = 0.12 * h
  const g = ctx.createRadialGradient(gx, gy, 0, gx, gy, Math.max(w, h))
  g.addColorStop(0, 'rgba(255,220,120,0.20)')
  g.addColorStop(0.3, 'rgba(255,210,100,0.12)')
  g.addColorStop(1, 'rgba(255,200,90,0)')
  ctx.fillStyle = g
  ctx.fillRect(0, 0, w, h)

  // 微尘
  particles.forEach(p => {
    p.x += p.vx * dt
    p.y += p.vy * dt
    if (p.x < -10) p.x = w + 10
    if (p.x > w + 10) p.x = -10
    if (p.y < -10) p.y = h + 10
    if (p.y > h + 10) p.y = -10
    ctx.beginPath()
    ctx.fillStyle = `rgba(255,255,255,${p.a})`
    ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2)
    ctx.fill()
  })
}

function renderWindy(dt) {
  const w = window.innerWidth, h = window.innerHeight
  // 顶部偏冷蓝色体积感
  const g = ctx.createLinearGradient(0, 0, 0, h)
  g.addColorStop(0, 'rgba(150,200,255,0.06)')
  g.addColorStop(1, 'rgba(0,0,0,0)')
  ctx.fillStyle = g
  ctx.fillRect(0, 0, w, h)

  particles.forEach(p => {
    if (p.type === 'wind') {
      p.x += p.v * dt
      if (p.x > w + p.len) p.x = -p.len
      ctx.strokeStyle = `rgba(200,230,255,${p.a})`
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.moveTo(p.x, p.y)
      ctx.lineTo(p.x - p.len, p.y)
      ctx.stroke()
    } else {
      // speck
      p.x += p.vx * dt
      p.y += p.vy * dt * 0.2
      if (p.x > w + 10) p.x = -10
      if (p.y < -10) p.y = h + 10
      if (p.y > h + 10) p.y = -10
      ctx.fillStyle = `rgba(255,255,255,${p.a})`
      ctx.fillRect(p.x, p.y, p.r, p.r)
    }
  })
}

function renderStorm(dt) {
  const w = window.innerWidth, h = window.innerHeight
  // 背景冷色雾
  const g = ctx.createLinearGradient(0, 0, 0, h)
  g.addColorStop(0, 'rgba(200,220,255,0.05)')
  g.addColorStop(1, 'rgba(0,0,0,0)')
  ctx.fillStyle = g
  ctx.fillRect(0, 0, w, h)

  // 雨幕
  ctx.strokeStyle = 'rgba(180,210,255,0.6)'
  particles.forEach(p => {
    p.y += p.v * dt
    p.x += p.drift * dt * 0.2
    if (p.y > h + 20) { p.y = -20; p.x = Math.random() * w }
    if (p.x < -20) p.x = w + 20
    ctx.globalAlpha = p.a
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(p.x, p.y)
    ctx.lineTo(p.x, p.y - p.len)
    ctx.stroke()
    ctx.globalAlpha = 1
  })

  // 闪电微弱反射（与现有 CSS 闪电互补，不冲突）
  ctx.fillStyle = 'rgba(255,255,255,0.03)'
  ctx.fillRect(0, 0, w, Math.min(0.18 * h, 160))
}

function renderBlizzard(dt, t) {
  const w = window.innerWidth, h = window.innerHeight
  // 近地轻雾
  const g = ctx.createLinearGradient(0, h * 0.7, 0, h)
  g.addColorStop(0, 'rgba(255,255,255,0)')
  g.addColorStop(1, 'rgba(255,255,255,0.10)')
  ctx.fillStyle = g
  ctx.fillRect(0, h * 0.7, w, h * 0.3)

  particles.forEach(p => {
    p.phi += p.sway * dt * 0.6
    p.x += Math.sin(p.phi) * 14 * dt + p.vx * dt * 0.2
    p.y += p.vy * dt
    if (p.y > h + 10) { p.y = -10; p.x = Math.random() * w }
    if (p.x < -10) p.x = w + 10
    if (p.x > w + 10) p.x = -10
    ctx.beginPath()
    ctx.fillStyle = `rgba(255,255,255,${p.a})`
    ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2)
    ctx.fill()
  })
}

function tick(ts) {
  if (!ctx) return
  const t = ts / 1000
  const dt = Math.min(0.033, t - (lastT || t))
  lastT = t

  // 清屏（渐隐清除，带出体积叠加柔和效果）
  ctx.clearRect(0, 0, window.innerWidth, window.innerHeight)

  const w = activeWeather.value
  if (w === 'sunny') renderSunny(dt)
  else if (w === 'windy') renderWindy(dt)
  else if (w === 'storm') renderStorm(dt)
  else if (w === 'blizzard') renderBlizzard(dt, t)

  rafId = requestAnimationFrame(tick)
}

function startFx() {
  resizeCanvas()
  createParticlesFor(activeWeather.value)
  lastT = 0
  cancelAnimationFrame(rafId)
  rafId = requestAnimationFrame(tick)
}

function stopFx() {
  cancelAnimationFrame(rafId)
}

watch(activeWeather, () => {
  createParticlesFor(activeWeather.value)
})

onMounted(() => {
  // 绑定容器引用
  containerRef.value = document.querySelector('.cards-container')
  if (containerRef.value) {
    containerRef.value.addEventListener('scroll', onScroll, { passive: true })
  }
  window.addEventListener('keydown', onKeydown)

  // Canvas FX
  startFx()
  window.addEventListener('resize', resizeCanvas)
  setTimeout(() => setActive('sunny'), 300)
})

onBeforeUnmount(() => {
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', onScroll)
  }
  window.removeEventListener('keydown', onKeydown)
  window.removeEventListener('resize', resizeCanvas)
  stopFx()
})
</script>

<style scoped>
/* 基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.weather-app {
  min-height: 100vh;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  /* 默认深色基底，具体天气用 bg-* 类覆盖 */
  background: linear-gradient(135deg, #050509 0%, #0d0d12 100%);
}
/* 根背景跟随天气变化：确保“整屏都是天气” */
.weather-app.bg-sunny { background: linear-gradient(135deg, #060608, #0d0d12), radial-gradient(80rem 40rem at 20% 10%, rgba(255,224,150,.12), transparent 60%) no-repeat fixed; }
.weather-app.bg-windy { background: linear-gradient(135deg, #05070a, #0a0e14), radial-gradient(70rem 30rem at 60% 10%, rgba(120,190,255,.08), transparent 60%) no-repeat fixed; }
.weather-app.bg-storm { background: linear-gradient(135deg, #0a0a11, #0a0b12), radial-gradient(60rem 40rem at 50% 0%, rgba(255,255,255,.05), transparent 60%) no-repeat fixed; }
.weather-app.bg-blizzard { background: linear-gradient(135deg, #08090c, #0d0f13), radial-gradient(80rem 50rem at 50% 20%, rgba(255,255,255,.06), transparent 60%) no-repeat fixed; }

/* 全屏天气背景（最低层） */
.global-bg {
  position: fixed;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

/* 2D Canvas FX 层：用于体积光/风线/雨幕/雪（位于全屏背景之上，内容之下） */
.fx-canvas {
  position: fixed;
  inset: 0;
  z-index: 2;
  pointer-events: none;
  filter: saturate(112%) contrast(102%);
  will-change: transform;
  mix-blend-mode: screen; /* 与底层渐变/全局背景叠加，增强体积感 */
}

/* sunny 全屏背景 */
.sunny-global {
  background: radial-gradient(circle at 20% 10%, rgba(255,213,130,.08), transparent 40%),
              radial-gradient(circle at 80% 20%, rgba(255,255,255,.06), transparent 45%);
  mix-blend-mode: screen;
}
.global-sun {
  position: absolute;
  top: 8%;
  left: 12%;
  width: 140px;
  height: 140px;
  border-radius: 50%;
  background: radial-gradient(circle, #fff7b3 0%, #ffd15e 35%, rgba(255,209,94,.0) 70%);
  filter: blur(2px);
  animation: pulse 3s ease-in-out infinite;
}
.global-glow {
  position: absolute;
  top: 8%;
  left: 12%;
  width: 380px;
  height: 380px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,210,120,.22), rgba(255,210,120,0) 70%);
  filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}
.global-dust {
  position: absolute;
  top: calc(10% + var(--delay) * 4%);
  left: calc(20% + var(--delay) * 6%);
  width: 6px; height: 6px;
  border-radius: 50%;
  background: rgba(255,255,255,.25);
  animation: sparkle 3.2s ease-in-out infinite;
  animation-delay: var(--delay);
}

/* windy 全屏背景 */
.windy-global {
  background: radial-gradient(circle at 60% 10%, rgba(139,197,255,.06), transparent 40%);
}
.global-wind {
  position: absolute;
  left: -20%;
  right: -20%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,.35), transparent);
  top: 30%;
  animation: wind-flow 2.4s ease-in-out infinite;
  animation-delay: var(--delay);
  opacity: .8;
}
.global-cloud {
  position: absolute;
  background: rgba(255,255,255,.12);
  border-radius: 60px;
  filter: blur(2px);
  animation: cloud-drift 12s ease-in-out infinite;
}
.gc-1 { width: 140px; height: 70px; top: 18%; left: 65%; }
.gc-2 { width: 100px; height: 50px; top: 38%; left: 78%; animation-delay: 2s; }

/* storm 全屏背景 */
.storm-global {
  background: radial-gradient(circle at 50% 0%, rgba(255,255,255,.03), transparent 60%),
              linear-gradient(180deg, rgba(20,20,30,.4), rgba(10,10,16,.8));
}
.global-rain {
  position: absolute;
  top: -10%;
  width: 2px;
  height: 18vh;
  background: linear-gradient(to bottom, rgba(170,210,255,.7), rgba(120,160,255,0));
  transform: translateY(-20px);
  animation: global-rain-fall 1.1s linear infinite;
  animation-delay: var(--delay);
  opacity: .75;
}
@keyframes global-rain-fall {
  0% { transform: translateY(-20px); opacity: .8; }
  100% { transform: translateY(120vh); opacity: 0; }
}
.global-flash {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 70% 10%, rgba(255,255,255,.2), rgba(255,255,255,0) 40%);
  mix-blend-mode: screen;
  opacity: 0;
  animation: flash 5s ease-in-out infinite;
}
.global-mist {
  position: absolute;
  bottom: -10%;
  left: 0; right: 0;
  height: 40vh;
  background: linear-gradient(180deg, rgba(180,195,220,.08), rgba(180,195,220,0));
  filter: blur(6px);
}

/* snow 全屏背景 */
.snow-global {
  background: radial-gradient(circle at 50% 20%, rgba(255,255,255,.06), transparent 50%);
}
.global-snow {
  position: absolute;
  top: -5%;
  width: 6px; height: 6px;
  border-radius: 50%;
  background: rgba(255,255,255,.85);
  box-shadow: 0 0 8px rgba(255,255,255,.5);
  animation: global-snow-fall 6s linear infinite;
  animation-delay: var(--delay);
}
@keyframes global-snow-fall {
  0% { transform: translateY(-10px) translateX(0) rotate(0deg); opacity: .95; }
  100% { transform: translateY(110vh) translateX(-20px) rotate(240deg); opacity: 0; }
}
.global-haze {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 50% 80%, rgba(255,255,255,.08), rgba(255,255,255,0) 60%);
  filter: blur(4px);
}
.sr-only {
  position: absolute !important;
  width: 1px; height: 1px;
  padding: 0; margin: -1px;
  overflow: hidden; clip: rect(0,0,0,0);
  white-space: nowrap; border: 0;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  height: 80px;
  backdrop-filter: saturate(180%) blur(20px);
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 3; /* 位于 Canvas FX 之上 */
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.title {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff, #a0a0a0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
}

.spacer {
  width: 120px;
}

/* 卡片容器 */
.cards-container {
  position: relative;
  z-index: 3; /* 位于 Canvas FX 之上 */
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: minmax(360px, 1fr);
  gap: 24px;
  padding: 32px 24px;
  overflow-x: auto;
  scroll-snap-type: x proximity; /* 使用 proximity 以便自定义磁吸 */
  scroll-padding-inline: 24px;
  flex: 1;
  align-items: center;
  scroll-behavior: smooth;
}

.cards-container::-webkit-scrollbar {
  height: 6px;
}

.cards-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.cards-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

/* 天气卡片 */
.weather-card {
  position: relative;
  height: 520px;
  border-radius: 28px;
  overflow: hidden;
  scroll-snap-align: center;
  cursor: pointer;
  transition: transform .5s cubic-bezier(.2,.7,.2,1), box-shadow .4s ease, opacity .4s ease;
  transform: scale(0.95) translateY(10px);
  opacity: 0.85;
  backdrop-filter: saturate(180%) blur(12px);
  border: 1px solid rgba(255,255,255,0.12);
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,0.15),
    0 20px 60px rgba(0,0,0,0.35);
}

.weather-card.active {
  transform: translateY(0) scale(1.02);
  opacity: 1;
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,0.2),
    0 26px 80px rgba(0,0,0,0.45);
}

/* 吸附到位时轻微回弹 */
.weather-card.snap-bounce {
  animation: snap-bounce 160ms cubic-bezier(0.2, 0.8, 0.2, 1);
}
@keyframes snap-bounce {
  0% { transform: scale(1.01) translateY(0); }
  100% { transform: scale(1) translateY(0); }
}

.weather-card.tap {
  transform: scale(0.98);
}

.card-bg {
  position: absolute;
  inset: 0;
  z-index: 0;
}

.card-content {
  position: relative;
  z-index: 2;
  height: 100%;
  padding: 28px 28px 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 顶部信息区域的分隔与对齐 */
.card-content::after {
  content: '';
  position: absolute;
  left: 28px; right: 28px;
  top: 108px;
  height: 1px;
  background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.16), rgba(255,255,255,0));
  pointer-events: none;
}
.card-bg [aria-hidden="true"] {
  pointer-events: none;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 32px;
}

.icon-section {
  flex-shrink: 0;
}

.weather-icon {
  width: 64px;
  height: 64px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(0 6px 18px rgba(0,0,0,.25));
}

.temp-section {
  flex: 1;
}

.temperature {
  font-size: 58px;
  font-weight: 900;
  line-height: 1;
  margin-bottom: 6px;
  letter-spacing: -0.5px;
  text-shadow:
    0 6px 24px rgba(0,0,0,.35),
    0 1px 0 rgba(255,255,255,.12);
}

.weather-type {
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  color: rgba(255,255,255,.92);
  padding: 4px 10px;
  border-radius: 12px;
  background: rgba(255,255,255,.12);
  border: 1px solid rgba(255,255,255,.22);
  backdrop-filter: blur(8px);
  margin-bottom: 6px;
}

.location {
  font-size: 13px;
  color: rgba(255,255,255,.85);
  font-weight: 600;
  letter-spacing: .2px;
  opacity: .9;
}

.details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 14px 16px;
  margin-top: 6px;
}

.detail {
  position: relative;
  padding: 16px 18px;
  border-radius: 16px;
  background: linear-gradient(180deg, rgba(255,255,255,.10), rgba(255,255,255,.06));
  border: 1px solid rgba(255,255,255,.18);
  backdrop-filter: blur(12px);
  display: flex;
  flex-direction: column;
  gap: 6px;
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,.25),
    0 10px 30px rgba(0,0,0,.18);
  overflow: hidden;
}
.detail::after {
  content: '';
  position: absolute;
  right: 0; top: 0;
  width: 54px; height: 20px;
  border-bottom-left-radius: 999px;
  background: linear-gradient(90deg, rgba(255,255,255,.24), rgba(255,255,255,0));
  opacity: .55;
  filter: blur(.3px);
  pointer-events: none;
}

.label {
  font-size: 12px;
  color: rgba(255,255,255,.72);
  font-weight: 600;
  letter-spacing: .2px;
}

.value {
  font-size: 18px;
  color: #ffffff;
  font-weight: 800;
  letter-spacing: .2px;
  text-shadow: 0 2px 10px rgba(0,0,0,.25);
}

/* 晴天样式 */
.sunny-bg {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.sun-rays {
  position: absolute;
  inset: -20px;
  animation: rotate 20s linear infinite;
}

.ray {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120px;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform-origin: 0 50%;
  transform: translate(0, -50%) rotate(var(--angle));
  border-radius: 2px;
}

.particles {
  position: absolute;
  inset: 0;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: sparkle 3s ease-in-out infinite;
  animation-delay: var(--delay);
}

.particle:nth-child(1) { top: 20%; left: 20%; }
.particle:nth-child(2) { top: 30%; right: 25%; }
.particle:nth-child(3) { top: 60%; left: 30%; }
.particle:nth-child(4) { bottom: 30%; right: 20%; }
.particle:nth-child(5) { top: 45%; left: 65%; }
.particle:nth-child(6) { bottom: 25%; left: 45%; }

.sun-icon {
  animation: float 4s ease-in-out infinite;
}

.sun-core {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  background: radial-gradient(circle at 40% 35%, #ffffff 0%, #fff6a0 35%, #ffd35a 70%, #ffc14a 100%);
  box-shadow:
    0 0 24px rgba(255, 214, 110, 0.7),
    inset 0 2px 6px rgba(255,255,255,0.6);
}

.sun-glow {
  position: absolute;
  inset: -14px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 235, 130, 0.45) 0%, rgba(255, 235, 130, 0.0) 70%);
  animation: pulse 2.4s ease-in-out infinite;
}

/* 大风样式 */
.windy-bg {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.wind-lines {
  position: absolute;
  inset: 0;
}

.wind-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  border-radius: 1px;
  animation: wind-flow 2s ease-in-out infinite;
  animation-delay: var(--delay);
  top: calc(25% + var(--delay) * 50px);
  left: -20%;
  right: -20%;
}

.clouds {
  position: absolute;
  inset: 0;
}

.cloud {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  animation: cloud-drift 6s ease-in-out infinite;
}

.cloud-1 {
  width: 60px;
  height: 30px;
  top: 25%;
  left: 20%;
}

.cloud-2 {
  width: 45px;
  height: 22px;
  top: 55%;
  right: 25%;
  animation-delay: 2s;
}

.wind-spiral {
  width: 44px;
  height: 44px;
  border: 3px solid rgba(255,255,255,0.85);
  border-radius: 50%;
  border-top-color: transparent;
  border-right-color: transparent;
  box-shadow: 0 8px 20px rgba(0,0,0,.2);
  animation: spin 1.8s linear infinite;
}

/* 暴雨样式 */
.storm-bg {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.rain-drops {
  position: absolute;
  inset: 0;
}

.drop {
  position: absolute;
  width: 2px;
  height: 12px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(79, 172, 254, 0.6));
  border-radius: 0 0 2px 2px;
  animation: rain-fall 1s linear infinite;
  animation-delay: var(--delay);
  top: 50%;
  left: calc(20% + var(--offset));
}

.lightning {
  position: absolute;
  inset: 0;
  background: radial-gradient(120px 60px at 70% 10%, rgba(255,255,255,.14), rgba(255,255,255,0));
  opacity: 0;
  animation: flash 4s ease-in-out infinite;
  mix-blend-mode: screen;
}

.rain-cloud {
  width: 45px;
  height: 25px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(200, 200, 200, 0.8));
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.rain-streams {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 45px;
}

.stream {
  position: absolute;
  width: 1px;
  height: 15px;
  background: linear-gradient(to bottom, rgba(79, 172, 254, 0.8), transparent);
  border-radius: 0 0 1px 1px;
  animation: stream-drop 0.6s linear infinite;
  animation-delay: var(--delay);
  left: calc(var(--delay) * 8px);
}

/* 暴雪样式 */
.snow-bg {
  background: linear-gradient(160deg, #ede9e3 0%, #dcdad6 50%, #d2d1ce 100%);
}

.snowflakes {
  position: absolute;
  inset: 0;
}

.snowflake {
  position: absolute;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  animation: snow-fall 3s linear infinite;
  animation-delay: var(--delay);
  top: 45%;
  left: calc(15% + var(--offset));
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

.snow-crystal {
  position: relative;
  width: 56px;
  height: 56px;
  animation: crystal-rotate 8s linear infinite;
  filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.4));
}

.crystal-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(200, 230, 255, 0.9) 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow:
    0 0 8px rgba(255, 255, 255, 0.8),
    inset 0 1px 2px rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.crystal-arm {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: 0 50%;
  transform: translate(0, -50%) rotate(var(--angle));
  border-radius: 1px;
}

.crystal-arm.main {
  width: 24px;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(200, 230, 255, 0.8) 70%,
    transparent 100%);
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
}

.crystal-arm.secondary {
  width: 16px;
  height: 1.5px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(200, 230, 255, 0.5) 60%,
    transparent 100%);
}

.crystal-branch {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: 0 50%;
  transform: translate(0, -50%) rotate(var(--angle));
}

.branch-left,
.branch-right {
  position: absolute;
  width: 8px;
  height: 1px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.6), transparent);
  transform-origin: 0 50%;
}

.branch-left {
  top: -3px;
  left: 12px;
  transform: rotate(-30deg);
}

.branch-right {
  top: 3px;
  left: 12px;
  transform: rotate(30deg);
}

.crystal-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform-origin: 0 50%;
  transform: translate(-1px, -1px) rotate(var(--angle)) translateX(var(--distance));
  box-shadow: 0 0 3px rgba(255, 255, 255, 0.6);
}

.crystal-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: crystal-glow 3s ease-in-out infinite;
  z-index: -1;
}

/* 底部导航 */
.navigation {
  position: relative;
  z-index: 3; /* 位于 Canvas FX 之上 */
  padding: 24px;
  backdrop-filter: saturate(180%) blur(20px);
  background: rgba(0, 0, 0, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-dots {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.nav-dot {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 18px;
  overflow: hidden;
}
.nav-dot .dot-icon {
  position: relative;
  z-index: 2;
  transition: transform .25s ease, opacity .25s ease;
}
.nav-dot .dot-ripple {
  position: absolute;
  inset: 0;
  border-radius: 24px;
  background: radial-gradient(closest-side, rgba(255,255,255,.35), rgba(255,255,255,0) 70%);
  opacity: 0;
  transform: scale(0.6);
  z-index: 1;
}
.nav-dot.active .dot-ripple {
  animation: dot-ripple 600ms ease-out;
}
.nav-dot.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.25);
}
.nav-dot:not(.active) .dot-icon {
  opacity: .85;
}
@keyframes dot-ripple {
  0% { opacity: .0; transform: scale(0.6); }
  20% { opacity: .5; transform: scale(0.9); }
  100% { opacity: 0; transform: scale(1.3); }
}

.nav-dot:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.nav-dot.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.25);
}

/* 动画关键帧 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes sparkle {
  0%, 100% { opacity: 0.4; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-6px); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

@keyframes wind-flow {
  0% { transform: translateX(-100%); opacity: 0; }
  20% { opacity: 0.8; }
  80% { opacity: 0.8; }
  100% { transform: translateX(200%); opacity: 0; }
}

@keyframes cloud-drift {
  0%, 100% { transform: translateX(0) translateY(0); }
  33% { transform: translateX(10px) translateY(-3px); }
  66% { transform: translateX(-5px) translateY(3px); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes rain-fall {
  0% { transform: translateY(0); opacity: 0.8; }
  100% { transform: translateY(150px); opacity: 0; }
}

@keyframes flash {
  0%, 90%, 100% { opacity: 0; }
  91%, 93%, 95% { opacity: 0.6; }
  92%, 94% { opacity: 0.2; }
}

@keyframes stream-drop {
  0% { transform: translateY(0); opacity: 0.9; }
  100% { transform: translateY(25px); opacity: 0; }
}

@keyframes snow-fall {
  0% { transform: translateY(0) rotate(0deg); opacity: 0.9; }
  100% { transform: translateY(180px) rotate(180deg); opacity: 0; }
}

@keyframes crystal-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes crystal-glow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-container {
    grid-auto-columns: minmax(300px, 1fr);
    gap: 20px;
    padding: 24px 16px;
  }
  
  .weather-card {
    height: 450px;
  }
  
  .temperature {
    font-size: 48px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 16px 20px;
    height: 70px;
  }
  
  .cards-container {
    grid-auto-columns: minmax(280px, 1fr);
    gap: 16px;
    padding: 20px 12px;
  }
  
  .weather-card {
    height: 400px;
  }
  
  .card-content {
    padding: 24px;
  }
  
  .temperature {
    font-size: 42px;
  }
  
  .weather-type {
    font-size: 16px;
  }
}
</style>