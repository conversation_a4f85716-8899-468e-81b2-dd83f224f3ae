import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 3000,
    open: true,
    cors: true,
    proxy: {
      // 只代理 /api/ 开头的路径（注意末尾的斜杠）
      '^/api/': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      // 和风天气API代理
      '/qweather-api': {
        target: 'https://n34ky39weh.re.qweatherapi.com',
        changeOrigin: true,
        secure: true,
        timeout: 10000, // 10秒超时
        rewrite: (path) => path.replace(/^\/qweather-api/, '')
      },
      // 地理编码API代理 - 使用更稳定的服务
      '/nominatim-api': {
        target: 'https://nominatim.openstreetmap.org',
        changeOrigin: true,
        secure: true,
        timeout: 8000,
        headers: {
          'User-Agent': 'ai-creative-platform/1.0'
        },
        rewrite: (path) => path.replace(/^\/nominatim-api/, ''),
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('代理请求:', req.method, req.url)
          })
        }
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 使用现代Sass API
        api: 'modern-compiler',
        // 全局导入变量、混合器和响应式工具
        additionalData: `@use "@/styles/variables.scss" as *; @use "@/styles/mixins.scss" as *; @use "@/styles/responsive.scss" as *;`
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    // 增加 chunk 大小限制警告阈值
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
        // 手动分包优化
        manualChunks: {
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'element-vendor': ['element-plus', '@element-plus/icons-vue'],
          'utils-vendor': ['axios', 'dayjs', 'lodash-es']
        }
      }
    }
  }
})
