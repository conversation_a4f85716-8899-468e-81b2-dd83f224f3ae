<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qwen-image API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .loading {
            border-left-color: #ffc107;
            background-color: #fff3cd;
            color: #856404;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .image-result {
            margin-top: 15px;
            text-align: center;
        }
        .image-result img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Qwen-image API 测试工具</h1>
        
        <div class="test-section">
            <h3>📡 API 连接测试</h3>
            <p>测试 Qwen-image API 服务是否可用</p>
            <button onclick="testConnection()">测试连接</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🖼️ 图像生成测试</h3>
            <p>测试 Qwen-image 图像生成功能</p>
            <input type="text" id="imagePrompt" placeholder="输入图像描述，例如：一只可爱的小猫在花园里玩耍" value="一只可爱的小猫在花园里玩耍">
            <button onclick="testImageGeneration()">生成图像</button>
            <div id="imageResult" class="result" style="display: none;"></div>
            <div id="imageDisplay" class="image-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💬 对话测试</h3>
            <p>测试 Qwen-image 对话功能</p>
            <input type="text" id="chatPrompt" placeholder="输入对话内容，例如：你好，请介绍一下自己" value="你好，请介绍一下自己">
            <button onclick="testChat()">发送对话</button>
            <div id="chatResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🌊 流式对话测试</h3>
            <p>测试 Qwen-image 流式对话功能</p>
            <input type="text" id="streamPrompt" placeholder="输入对话内容，例如：请写一首关于春天的诗" value="请写一首关于春天的诗">
            <button onclick="testStreamChat()">开始流式对话</button>
            <div id="streamResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script type="module">
        // 导入 Qwen-image API
        import qwenImageApi from './src/services/qwenImageApi.js';

        // 将测试函数挂载到全局
        window.testConnection = async function() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试连接...';

            try {
                // 测试简单的连接
                const response = await fetch('http://*************:8020/', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ API 服务连接成功！\n状态码: ' + response.status;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ API 服务连接失败\n状态码: ' + response.status;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 连接测试失败:\n' + error.message;
            }
        };

        window.testImageGeneration = async function() {
            const prompt = document.getElementById('imagePrompt').value;
            const resultDiv = document.getElementById('imageResult');
            const imageDiv = document.getElementById('imageDisplay');
            
            if (!prompt.trim()) {
                alert('请输入图像描述');
                return;
            }

            resultDiv.style.display = 'block';
            imageDiv.style.display = 'none';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在生成图像，请稍候...';

            try {
                const result = await qwenImageApi.createChatCompletion({
                    model: 'qwen-plus',
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    temperature: 0.7,
                    stream: false,
                    user: `test-session-${Date.now()}`
                });

                console.log('Qwen-image API 响应:', result);

                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 图像生成成功！\n\n响应内容:\n' + JSON.stringify(result.data, null, 2);
                    
                    // 检查是否有图像
                    if (result.hasImage && result.image) {
                        imageDiv.style.display = 'block';
                        imageDiv.innerHTML = `<img src="${result.image}" alt="生成的图像" />`;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 图像生成失败:\n' + (result.message || '未知错误');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 图像生成测试失败:\n' + error.message + '\n\n错误详情:\n' + error.stack;
            }
        };

        window.testChat = async function() {
            const prompt = document.getElementById('chatPrompt').value;
            const resultDiv = document.getElementById('chatResult');
            
            if (!prompt.trim()) {
                alert('请输入对话内容');
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在处理对话，请稍候...';

            try {
                const result = await qwenImageApi.createChatCompletion({
                    model: 'qwen-plus',
                    messages: [
                        { role: 'user', content: prompt }
                    ],
                    temperature: 0.7,
                    stream: false,
                    user: `test-session-${Date.now()}`
                });

                console.log('Qwen-image 对话响应:', result);

                if (result.success) {
                    resultDiv.className = 'result success';
                    const content = result.data.choices?.[0]?.message?.content || '无回复内容';
                    resultDiv.textContent = '✅ 对话成功！\n\n回复内容:\n' + content + '\n\n完整响应:\n' + JSON.stringify(result.data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 对话失败:\n' + (result.message || '未知错误');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 对话测试失败:\n' + error.message + '\n\n错误详情:\n' + error.stack;
            }
        };

        window.testStreamChat = async function() {
            const prompt = document.getElementById('streamPrompt').value;
            const resultDiv = document.getElementById('streamResult');
            
            if (!prompt.trim()) {
                alert('请输入对话内容');
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在开始流式对话...';

            try {
                let fullContent = '';
                let reasoningContent = '';

                await qwenImageApi.createChatCompletionStream(
                    {
                        model: 'qwen-plus',
                        messages: [
                            { role: 'user', content: prompt }
                        ],
                        temperature: 0.7,
                        user: `test-session-${Date.now()}`
                    },
                    // onMessage
                    (content, isReasoning = false) => {
                        if (isReasoning) {
                            reasoningContent += content;
                            resultDiv.textContent = '🤔 推理中...\n' + reasoningContent + '\n\n回复内容:\n' + fullContent;
                        } else {
                            fullContent += content;
                            resultDiv.className = 'result success';
                            resultDiv.textContent = '✅ 流式对话进行中...\n\n推理内容:\n' + reasoningContent + '\n\n回复内容:\n' + fullContent;
                        }
                    },
                    // onError
                    (error) => {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = '❌ 流式对话失败:\n' + error.message;
                    },
                    // onComplete
                    () => {
                        resultDiv.className = 'result success';
                        resultDiv.textContent = '✅ 流式对话完成！\n\n推理内容:\n' + reasoningContent + '\n\n最终回复:\n' + fullContent;
                    }
                );
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 流式对话测试失败:\n' + error.message + '\n\n错误详情:\n' + error.stack;
            }
        };
    </script>
</body>
</html>