<template>
  <div class="login-view">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <div class="login-container">
      <!-- 左侧登录表单 -->
      <div class="login-card">
        <!-- 返回首页按钮 -->
        <div class="back-home">
          <button @click="$router.push('/')" class="back-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            返回首页
          </button>
        </div>

        <div class="login-header">
          <div class="logo-section">
            <div class="logo-icon">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                <path d="M9 9h.01M15 9h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <h1>欢迎回来</h1>
            <p class="subtitle">登录您的账户，继续您的创作之旅</p>
          </div>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          @submit.prevent="handleLogin"
        >
          <div class="form-group">
            <label class="form-label">用户名</label>
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名/邮箱/手机号"
                size="large"
                :prefix-icon="User"
                clearable
                class="custom-input"
              />
            </el-form-item>
          </div>

          <div class="form-group">
            <label class="form-label">密码</label>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                :prefix-icon="Lock"
                show-password
                clearable
                class="custom-input"
                @keyup.enter="handleLogin"
              />
            </el-form-item>
          </div>

          <el-form-item prop="captcha" v-if="showCaptcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="验证码"
                size="large"
                :prefix-icon="Key"
                clearable
                class="captcha-input"
              />
              <div class="captcha-image" @click="refreshCaptcha">
                <img :src="captchaUrl" alt="验证码" />
              </div>
            </div>
          </el-form-item>

          <div class="form-options">
            <el-checkbox v-model="loginForm.rememberMe" class="custom-checkbox">
              记住我
            </el-checkbox>
            <button type="button" @click="$router.push('/forgot-password')" class="forgot-link">
              忘记密码？
            </button>
          </div>

          <button
            type="button"
            :disabled="loading"
            @click="handleLogin"
            class="login-btn"
          >
            <span v-if="loading" class="loading-spinner"></span>
            {{ loading ? '登录中...' : '登录' }}
          </button>

          <div class="divider">
            <span>或使用以下方式登录</span>
          </div>

          <div class="social-login">
            <button
              v-for="provider in socialProviders"
              :key="provider.name"
              @click="handleSocialLogin(provider.name)"
              class="social-btn"
              :class="provider.name"
              :disabled="loading"
            >
              <div class="social-icon" v-html="provider.icon"></div>
              {{ provider.label }}
            </button>
          </div>

          <div class="register-link">
            <span>还没有账户？</span>
            <button type="button" @click="$router.push('/register')" class="register-btn">
              立即注册
            </button>
          </div>
        </el-form>
      </div>

      <!-- 右侧展示区域 -->
      <div class="showcase-section">
        <div class="showcase-content">
          <div class="showcase-header">
            <h2>释放创意无限可能</h2>
            <p>与AI一起探索艺术创作的新境界</p>
          </div>

          <div class="feature-showcase">
            <div class="feature-card">
              <div class="feature-icon chat-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <h3>智能对话</h3>
              <p>与AI进行自然对话，获得创意灵感</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon art-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <h3>AI绘画</h3>
              <p>文字转图像，创造独特艺术作品</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon gallery-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                  <path d="M21 15l-5-5L5 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <h3>作品分享</h3>
              <p>展示您的创作，发现更多精彩</p>
            </div>
          </div>

          <div class="showcase-stats">
            <div class="stat-item">
              <span class="stat-number">10K+</span>
              <span class="stat-label">活跃用户</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">50K+</span>
              <span class="stat-label">创作作品</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">99%</span>
              <span class="stat-label">用户满意度</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Key } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
// import { isValidEmail, isValidPhone } from '@/utils/common'

const router = useRouter()
const route = useRoute()

// 使用响应式引用来安全地初始化 userStore
const userStore = ref(null)

// 响应式数据
const loginFormRef = ref()
const loading = ref(false)
const showCaptcha = ref(false)
const captchaUrl = ref('')

const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  rememberMe: false,
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 1, max: 50, message: '用户名长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码为4位', trigger: 'blur' },
  ],
}

// 第三方登录提供商
const socialProviders = [
  {
    name: 'google',
    label: 'Google 登录',
    icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
      <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
      <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
      <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
    </svg>`
  },
  {
    name: 'github',
    label: 'GitHub 登录',
    icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
    </svg>`
  },
  {
    name: 'wechat',
    label: '微信登录',
    icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
      <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-2.676-2.476-4.991-5.693-6.196a8.986 8.986 0 0 0-3.530-.44z" fill="currentColor"/>
      <path d="M23.759 14.441c0-3.193-2.63-5.79-5.862-5.79s-5.862 2.597-5.862 5.79c0 3.193 2.63 5.79 5.862 5.79a7.99 7.99 0 0 0 2.24-.322.68.68 0 0 1 .567.077l1.502.877a.257.257 0 0 0 .132.043c.129 0 .229-.102.229-.229 0-.057-.024-.111-.038-.168l-.308-1.169a.465.465 0 0 1 .168-.525c1.446-1.067 2.37-2.637 2.37-4.374z" fill="currentColor"/>
    </svg>`
  }
]

// 方法
const handleLogin = async () => {
  console.log('handleLogin 被调用')

  if (!loginFormRef.value) {
    console.log('loginFormRef 不存在')
    return
  }

  if (!loginForm.username || !loginForm.password) {
    ElMessage.error('请输入用户名和密码')
    return
  }

  // 确保 userStore 可用
  if (!userStore.value) {
    ElMessage.error('用户系统未初始化，请刷新页面重试')
    return
  }

  loading.value = true

  try {
    console.log('开始登录:', loginForm.username)

    const result = await userStore.value.login({
      username: loginForm.username,
      password: loginForm.password,
      captcha: showCaptcha.value ? loginForm.captcha : undefined,
      rememberMe: loginForm.rememberMe,
    })

    console.log('登录结果:', result)

    if (result.success) {
      ElMessage({
        message: '登录成功！欢迎回来',
        type: 'success',
        duration: 3000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })

      // 跳转到目标页面或首页
      const redirect = route.query.redirect || '/home'
      router.push(redirect)
    } else {
      // 登录失败，可能需要显示验证码
      if (result.needCaptcha) {
        showCaptcha.value = true
        refreshCaptcha()
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    console.error('错误详情:', error.message, error.stack)

    // 只有在 store 初始化失败时才显示系统错误
    if (error.message && error.message.includes('getActivePinia')) {
      ElMessage({
        message: '系统初始化失败，请刷新页面重试',
        type: 'error',
        duration: 3000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })
    } else {
      ElMessage({
        message: '登录失败，请稍后再试',
        type: 'error',
        duration: 3000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })
    }
  } finally {
    loading.value = false
  }
}

const handleSocialLogin = (provider) => {
  const providerNames = {
    google: 'Google',
    github: 'GitHub',
    wechat: '微信'
  }

  ElMessage({
    message: `${providerNames[provider]} 登录功能即将上线，敬请期待！`,
    type: 'info',
    duration: 3000,
    showClose: true
  })

  // TODO: 实现第三方登录
  // 这里将来会实现真正的第三方登录逻辑
  // 例如：window.location.href = `/api/auth/${provider}`
}

const refreshCaptcha = () => {
  captchaUrl.value = `/api/captcha?t=${Date.now()}`
}

// 用户名验证函数（备用）
// const validateUsername = (username) => {
//   if (isValidEmail(username)) {
//     return 'email'
//   } else if (isValidPhone(username)) {
//     return 'phone'
//   } else {
//     return 'username'
//   }
// }

// 生命周期
onMounted(async () => {
  try {
    console.log('LoginView: 组件已挂载')

    // 异步初始化 userStore
    const { useUserStore } = await import('@/stores')
    userStore.value = useUserStore()
    console.log('LoginView: userStore 初始化成功')

    // 检查登录状态
    if (userStore.value.isLoggedIn) {
      const redirect = route.query.redirect || '/home'
      router.push(redirect)
      return
    }

    // 检查记住我功能
    const rememberedUsername = userStore.value.getRememberMeInfo?.()
    if (rememberedUsername) {
      loginForm.username = rememberedUsername
      loginForm.rememberMe = true
    }
  } catch (error) {
    console.warn('LoginView: 初始化失败:', error)
    // 不阻止页面渲染，允许用户手动登录
  }

  // 检查是否需要显示验证码
  const failedAttempts = localStorage.getItem('login_failed_attempts') || 0
  if (failedAttempts >= 3) {
    showCaptcha.value = true
    refreshCaptcha()
  }
})
</script>

<style lang="scss" scoped>
.login-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;

  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;

    &.circle-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.circle-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }

    &.circle-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.login-container {
  display: flex;
  min-height: 100vh;
  position: relative;
  z-index: 2;
}

.login-card {
  flex: 1;
  max-width: 500px;
  padding: 3rem 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);

  .back-home {
    position: absolute;
    top: 2rem;
    left: 2rem;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.25rem;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 50px;
      color: #666;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: white;
        transform: translateX(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        color: #667eea;
      }

      svg {
        transition: transform 0.3s ease;
      }

      &:hover svg {
        transform: translateX(-3px);
      }
    }
  }

  .login-header {
    text-align: center;
    margin-bottom: 2.5rem;

    .logo-section {
      margin-bottom: 2rem;

      .logo-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        margin-bottom: 1.5rem;
        color: white;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
      }

      h1 {
        margin: 0 0 0.5rem 0;
        font-size: 2.2rem;
        font-weight: 700;
        color: #2d3748;
        letter-spacing: -0.02em;
      }

      .subtitle {
        margin: 0;
        color: #718096;
        font-size: 1rem;
        line-height: 1.5;
      }
    }
  }

  .login-form {
    .form-group {
      margin-bottom: 1.5rem;

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #4a5568;
        font-size: 0.9rem;
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 0;

      .el-form-item__error {
        font-size: 0.8rem;
        margin-top: 0.25rem;
      }
    }

    :deep(.custom-input) {
      .el-input__wrapper {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        box-shadow: none;

        &:hover {
          border-color: #cbd5e0;
          background: white;
        }

        &.is-focus {
          border-color: #667eea;
          background: white;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
      }

      .el-input__inner {
        font-size: 1rem;
        color: #2d3748;

        &::placeholder {
          color: #a0aec0;
        }
      }
    }

    .captcha-container {
      display: flex;
      gap: 1rem;

      .captcha-input {
        flex: 1;
      }

      .captcha-image {
        width: 120px;
        height: 48px;
        cursor: pointer;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        overflow: hidden;
        transition: border-color 0.3s ease;

        &:hover {
          border-color: #cbd5e0;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .form-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 1.5rem 0;

      :deep(.custom-checkbox) {
        .el-checkbox__label {
          color: #4a5568;
          font-size: 0.9rem;
        }
      }

      .forgot-link {
        background: none;
        border: none;
        color: #667eea;
        font-size: 0.9rem;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #5a67d8;
          text-decoration: underline;
        }
      }
    }

    .login-btn {
      width: 100%;
      height: 52px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      color: white;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin: 2rem 0 1.5rem 0;
      position: relative;
      overflow: hidden;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }

      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
        margin-right: 0.5rem;
      }
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .divider {
      text-align: center;
      margin: 2rem 0;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: #e2e8f0;
      }

      span {
        background: rgba(255, 255, 255, 0.95);
        padding: 0 1rem;
        color: #718096;
        font-size: 0.9rem;
        position: relative;
        z-index: 1;
      }
    }

    .social-login {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      margin-bottom: 2rem;

      .social-btn {
        width: 100%;
        height: 48px;
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        color: #4a5568;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;

        &:hover:not(:disabled) {
          border-color: #cbd5e0;
          background: #f7fafc;
          transform: translateY(-1px);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none !important;
        }

        .social-icon {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;

          :deep(svg) {
            width: 20px;
            height: 20px;
          }
        }

        &.google {
          .social-icon {
            color: #ea4335;
          }

          &:hover {
            border-color: #ea4335;
            color: #ea4335;
            background: rgba(234, 67, 53, 0.05);
            box-shadow: 0 4px 12px rgba(234, 67, 53, 0.15);
          }
        }

        &.github {
          .social-icon {
            color: #333;
          }

          &:hover {
            border-color: #333;
            color: #333;
            background: rgba(51, 51, 51, 0.05);
            box-shadow: 0 4px 12px rgba(51, 51, 51, 0.15);
          }
        }

        &.wechat {
          .social-icon {
            color: #07c160;
          }

          &:hover {
            border-color: #07c160;
            color: #07c160;
            background: rgba(7, 193, 96, 0.05);
            box-shadow: 0 4px 12px rgba(7, 193, 96, 0.15);
          }
        }
      }
    }

    .register-link {
      text-align: center;
      color: #718096;
      font-size: 0.9rem;

      .register-btn {
        background: none;
        border: none;
        color: #667eea;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        margin-left: 0.25rem;
        transition: color 0.3s ease;

        &:hover {
          color: #5a67d8;
          text-decoration: underline;
        }
      }
    }
  }
}

.showcase-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.05"><circle cx="30" cy="30" r="2"/></g></svg>');
    opacity: 0.3;
  }

  .showcase-content {
    text-align: center;
    max-width: 600px;
    position: relative;
    z-index: 1;

    .showcase-header {
      margin-bottom: 3rem;

      h2 {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1rem;
        line-height: 1.1;
        letter-spacing: -0.02em;
        background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      p {
        font-size: 1.25rem;
        opacity: 0.9;
        line-height: 1.6;
        margin: 0;
      }
    }

    .feature-showcase {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;

      .feature-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem 1.5rem;
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          background: rgba(255, 255, 255, 0.15);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
          width: 64px;
          height: 64px;
          margin: 0 auto 1rem;
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;

          &.chat-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.art-icon {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
          }

          &.gallery-icon {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
          }
        }

        h3 {
          font-size: 1.25rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
          color: white;
        }

        p {
          font-size: 0.95rem;
          opacity: 0.8;
          line-height: 1.5;
          margin: 0;
        }
      }
    }

    .showcase-stats {
      display: flex;
      justify-content: center;
      gap: 3rem;

      .stat-item {
        text-align: center;

        .stat-number {
          display: block;
          font-size: 2.5rem;
          font-weight: 800;
          color: white;
          margin-bottom: 0.25rem;
        }

        .stat-label {
          font-size: 0.9rem;
          opacity: 0.8;
          color: white;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }

  .login-card {
    max-width: none;
    min-height: 100vh;
  }

  .showcase-section {
    min-height: 60vh;
    padding: 2rem;

    .showcase-content {
      .showcase-header h2 {
        font-size: 2.5rem;
      }

      .feature-showcase {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .showcase-stats {
        gap: 2rem;

        .stat-item .stat-number {
          font-size: 2rem;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .login-card {
    padding: 2rem 1.5rem;

    .back-home {
      top: 1rem;
      left: 1rem;
    }

    .login-header {
      margin-bottom: 2rem;

      .logo-section {
        .logo-icon {
          width: 60px;
          height: 60px;
        }

        h1 {
          font-size: 1.8rem;
        }
      }
    }
  }

  .showcase-section {
    padding: 1.5rem;

    .showcase-content {
      .showcase-header {
        margin-bottom: 2rem;

        h2 {
          font-size: 2rem;
        }

        p {
          font-size: 1.1rem;
        }
      }

      .showcase-stats {
        flex-direction: column;
        gap: 1.5rem;
      }
    }
  }
}
</style>
