<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doubao AI 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 18px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
        }
        .result img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin: 10px 0;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #f44336;
            font-weight: bold;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Doubao AI API 测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 测试API连接</div>
            <button class="test-button" onclick="testConnection()">测试连接</button>
            <div id="connection-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 测试对话功能</div>
            <input type="text" class="test-input" id="chat-input" placeholder="输入消息，例如：你好" value="你好，请介绍一下自己">
            <button class="test-button" onclick="testChat()">发送消息</button>
            <div id="chat-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 测试生图功能</div>
            <input type="text" class="test-input" id="image-input" placeholder="输入图片描述，例如：一只可爱的猫咪" value="一只可爱的猫咪">
            <button class="test-button" onclick="testImageGeneration()">生成图片</button>
            <div id="image-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 测试流式对话</div>
            <input type="text" class="test-input" id="stream-input" placeholder="输入消息" value="请用100字介绍一下人工智能">
            <button class="test-button" onclick="testStreamChat()">流式发送</button>
            <div id="stream-result" class="result" style="display:none;"></div>
        </div>

        <div class="status" id="status">
            <strong>状态：</strong> 就绪
        </div>
    </div>

    <script>
        const API_URL = 'http://*************:8000';
        const API_KEY = '123456'; // 根据图2设置正确的API Key
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            const colorMap = {
                'info': '#333',
                'success': '#4CAF50',
                'error': '#f44336',
                'loading': '#FF9800'
            };
            status.innerHTML = `<strong style="color: ${colorMap[type]}">状态：</strong> ${message}`;
        }

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (type === 'error') {
                element.innerHTML = `<span class="error">${content}</span>`;
            } else if (type === 'success') {
                element.innerHTML = `<span class="success">${content}</span>`;
            } else {
                element.innerHTML = content;
            }
        }

        async function testConnection() {
            updateStatus('正在测试连接...', 'loading');
            const resultDiv = document.getElementById('connection-result');
            
            try {
                const response = await fetch(`${API_URL}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'doubao-pro-32k',
                        messages: [{ role: 'user', content: '你好' }],
                        stream: false,
                        temperature: 0.7
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult('connection-result', 
                        `✅ 连接成功！\n\n响应ID: ${data.id}\n模型: ${data.model}\n创建时间: ${new Date(data.created * 1000).toLocaleString()}`, 
                        'success'
                    );
                    updateStatus('连接测试成功', 'success');
                } else {
                    showResult('connection-result', 
                        `❌ 连接失败！状态码: ${response.status}`, 
                        'error'
                    );
                    updateStatus('连接测试失败', 'error');
                }
            } catch (error) {
                showResult('connection-result', 
                    `❌ 连接错误: ${error.message}`, 
                    'error'
                );
                updateStatus('连接测试出错', 'error');
            }
        }

        async function testChat() {
            updateStatus('正在发送消息...', 'loading');
            const input = document.getElementById('chat-input').value;
            const resultDiv = document.getElementById('chat-result');
            
            try {
                const response = await fetch(`${API_URL}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'doubao-pro-32k',
                        messages: [{ role: 'user', content: input }],
                        stream: false,
                        temperature: 0.7
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0].message.content;
                    
                    // 处理可能包含的图片
                    const processedContent = content.replace(
                        /!\[([^\]]*)\]\(([^)]+)\)/g,
                        '<img src="$2" alt="$1" />'
                    );
                    
                    showResult('chat-result', 
                        `<strong>AI回复：</strong>\n\n${processedContent}`
                    );
                    updateStatus('对话测试成功', 'success');
                } else {
                    const error = await response.text();
                    showResult('chat-result', 
                        `❌ 请求失败: ${error}`, 
                        'error'
                    );
                    updateStatus('对话测试失败', 'error');
                }
            } catch (error) {
                showResult('chat-result', 
                    `❌ 错误: ${error.message}`, 
                    'error'
                );
                updateStatus('对话测试出错', 'error');
            }
        }

        async function testImageGeneration() {
            updateStatus('正在生成图片...', 'loading');
            const input = document.getElementById('image-input').value;
            const resultDiv = document.getElementById('image-result');
            
            try {
                const response = await fetch(`${API_URL}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'doubao-pro-32k',
                        messages: [{ role: 'user', content: input }],
                        stream: false,
                        temperature: 0.7
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0].message.content;
                    
                    // 提取图片URL
                    const imageMatch = content.match(/!\[.*?\]\((.*?)\)/);
                    if (imageMatch && imageMatch[1]) {
                        const imageUrl = imageMatch[1];
                        
                        // 提取命令 - 新格式: q: 清晰 1754453316 1
                        const commands = [];
                        const lines = content.split('\n');
                        const commandMap = {
                            'q': '清晰',
                            'v': '编辑',
                            'k': '扩图',
                            's': '动起来',
                            'r': '重新生成'
                        };
                        
                        for (const line of lines) {
                            const trimmed = line.trim();
                            if (trimmed.match(/^[a-z]: .+/)) {
                                const [command, ...params] = trimmed.split(': ');
                                const commandName = commandMap[command] || command;
                                commands.push(`${command}: ${commandName} ${params.join(': ')}`);
                            }
                        }
                        
                        showResult('image-result',
                            `<strong>生成的图片：</strong>\n\n<img src="${imageUrl}" alt="生成的图片" />\n\n` +
                            (commands.length > 0 ? `<strong>可用命令：</strong>\n${commands.join('\n')}` : '')
                        );
                        updateStatus('图片生成成功', 'success');
                    } else {
                        showResult('image-result',
                            `<strong>AI回复（未检测到图片）：</strong>\n\n${content}`
                        );
                        updateStatus('未生成图片，但收到了文本回复', 'info');
                    }
                } else {
                    const error = await response.text();
                    showResult('image-result', 
                        `❌ 请求失败: ${error}`, 
                        'error'
                    );
                    updateStatus('图片生成失败', 'error');
                }
            } catch (error) {
                showResult('image-result', 
                    `❌ 错误: ${error.message}`, 
                    'error'
                );
                updateStatus('图片生成出错', 'error');
            }
        }

        async function testStreamChat() {
            updateStatus('正在建立流式连接...', 'loading');
            const input = document.getElementById('stream-input').value;
            const resultDiv = document.getElementById('stream-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<strong>AI回复（流式）：</strong>\n\n';
            
            try {
                const response = await fetch(`${API_URL}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: 'doubao-pro-32k',
                        messages: [{ role: 'user', content: input }],
                        stream: true,
                        temperature: 0.7
                    })
                });

                if (response.ok) {
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';
                    let fullContent = '';

                    updateStatus('正在接收流式数据...', 'loading');

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop() || '';

                        for (const line of lines) {
                            const trimmed = line.trim();
                            if (trimmed === '' || trimmed === 'data: [DONE]') continue;
                            
                            if (trimmed.startsWith('data: ')) {
                                try {
                                    const jsonStr = trimmed.slice(6);
                                    const data = JSON.parse(jsonStr);
                                    
                                    if (data.choices && data.choices[0] && data.choices[0].delta) {
                                        const content = data.choices[0].delta.content;
                                        if (content) {
                                            fullContent += content;
                                            resultDiv.innerHTML = `<strong>AI回复（流式）：</strong>\n\n${fullContent}`;
                                        }
                                    }
                                } catch (e) {
                                    console.warn('解析流数据失败:', e);
                                }
                            }
                        }
                    }

                    updateStatus('流式对话完成', 'success');
                } else {
                    const error = await response.text();
                    showResult('stream-result', 
                        `❌ 请求失败: ${error}`, 
                        'error'
                    );
                    updateStatus('流式对话失败', 'error');
                }
            } catch (error) {
                showResult('stream-result', 
                    `❌ 错误: ${error.message}`, 
                    'error'
                );
                updateStatus('流式对话出错', 'error');
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            console.log('Doubao AI 测试页面已加载');
            console.log('API地址:', API_URL);
        };
    </script>
</body>
</html>