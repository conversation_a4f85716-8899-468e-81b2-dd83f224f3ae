<template>
  <div
    class="message-item"
    :class="{
      'user-message': message.role === 'user',
      'ai-message': message.role === 'assistant',
      ...dynamicClasses
    }"
    :data-message-id="message.id"
  >
    <div class="message-avatar" v-if="message.role === 'assistant'">
      <div class="ai-avatar">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
    </div>

    <div class="message-content-wrapper">
      <div v-if="message.role === 'assistant'" class="message-header">
        <span class="sender-name">AI助手</span>
        <div class="message-status" :class="message.status">
          <div v-if="message.status === 'loading'" class="loading-dots">
            <span></span><span></span><span></span>
          </div>
          <span v-else-if="message.status === 'error'" class="error-text">发送失败</span>
        </div>
      </div>

      <div
        class="message-content"
        :class="contentClasses"
        :style="dynamicStyles"
        v-html="formattedContent"
      ></div>

      <div class="message-actions" v-if="message.role === 'assistant'">
        <div class="action-buttons">
          <button @click="$emit('copy-message', message.content)" class="action-btn" title="复制到剪贴板">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
          <button @click="$emit('regenerate-message', message.id)" class="action-btn" title="重新生成">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <button class="action-btn" title="URL上传">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="currentColor" stroke-width="2"/>
              <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
        </div>
        <span class="message-time">{{ formatTime(message.timestamp) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import { formatRelativeTime } from '@/utils/common'

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    default: 0
  },
  userAvatar: {
    type: String,
    default: null
  }
})

defineEmits(['copy-message', 'regenerate-message'])

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) {
        console.error('Highlight error:', err)
      }
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

const formattedContent = computed(() => {
  if (!props.message.content) return ''
  
  try {
    // 处理 Markdown 内容
    let html = marked(props.message.content)
    
    // 为代码块添加复制按钮
    html = html.replace(
      /<pre><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g,
      (_, lang, code) => {
        return `
          <div class="code-block">
            <div class="code-header">
              <span class="code-lang">${lang}</span>
              <button class="copy-code-btn" onclick="copyCode(this)">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                </svg>
                复制
              </button>
            </div>
            <pre><code class="language-${lang}">${code}</code></pre>
          </div>
        `
      }
    )
    
    return html
  } catch (error) {
    console.error('Markdown parsing error:', error)
    return props.message.content
  }
})

const formatTime = (timestamp) => {
  return formatRelativeTime(timestamp)
}

// 分析消息内容特征
const contentAnalysis = computed(() => {
  const content = props.message.content || ''
  const wordCount = content.length
  const lineCount = content.split('\n').length
  const hasCode = /```[\s\S]*?```|`[^`]+`/.test(content)
  const hasTable = /\|.*\|/.test(content)
  const hasList = /^[\s]*[-*+]\s|^[\s]*\d+\.\s/m.test(content)
  const hasQuote = /^[\s]*>/m.test(content)
  const hasEmoji = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(content)
  const hasUrl = /https?:\/\/[^\s]+/.test(content)
  const isQuestion = /[？?][\s]*$/.test(content.trim())
  const isExclamation = /[！!][\s]*$/.test(content.trim())
  const hasNumbers = /\d+/.test(content)
  const hasFormula = /\$.*\$|\\\(.*\\\)|\\\[.*\\\]/.test(content)

  // 判断内容类型
  let contentType = 'text'
  if (hasCode) contentType = 'code'
  else if (hasTable) contentType = 'table'
  else if (hasList) contentType = 'list'
  else if (hasQuote) contentType = 'quote'
  else if (hasFormula) contentType = 'formula'
  else if (isQuestion) contentType = 'question'
  else if (isExclamation) contentType = 'exclamation'

  // 判断内容长度级别
  let lengthLevel = 'short'
  if (wordCount > 500) lengthLevel = 'long'
  else if (wordCount > 200) lengthLevel = 'medium'

  return {
    wordCount,
    lineCount,
    hasCode,
    hasTable,
    hasList,
    hasQuote,
    hasEmoji,
    hasUrl,
    isQuestion,
    isExclamation,
    hasNumbers,
    hasFormula,
    contentType,
    lengthLevel
  }
})

// 动态CSS类
const dynamicClasses = computed(() => {
  const analysis = contentAnalysis.value
  return {
    [`content-${analysis.contentType}`]: true,
    [`length-${analysis.lengthLevel}`]: true,
    'has-emoji': analysis.hasEmoji,
    'has-code': analysis.hasCode,
    'has-table': analysis.hasTable,
    'has-list': analysis.hasList,
    'has-quote': analysis.hasQuote,
    'has-url': analysis.hasUrl,
    'has-numbers': analysis.hasNumbers,
    'has-formula': analysis.hasFormula,
    'is-question': analysis.isQuestion,
    'is-exclamation': analysis.isExclamation
  }
})

// 内容区域的动态类
const contentClasses = computed(() => {
  const analysis = contentAnalysis.value
  return {
    [`content-type-${analysis.contentType}`]: true,
    [`content-length-${analysis.lengthLevel}`]: true,
    'animated-content': analysis.hasEmoji || analysis.isExclamation,
    'code-heavy': analysis.hasCode && analysis.wordCount > 100,
    'data-rich': analysis.hasTable || analysis.hasNumbers,
    'interactive': analysis.hasUrl || analysis.isQuestion
  }
})

// 动态样式
const dynamicStyles = computed(() => {
  const analysis = contentAnalysis.value
  const styles = {}

  // 根据内容长度调整最大宽度
  if (analysis.lengthLevel === 'long') {
    styles.maxWidth = '85%'
  } else if (analysis.lengthLevel === 'short' && analysis.wordCount < 50) {
    styles.maxWidth = '60%'
  }

  // 根据内容类型调整样式
  if (analysis.contentType === 'code') {
    styles.fontFamily = "'Fira Code', 'JetBrains Mono', monospace"
  }

  if (analysis.contentType === 'question') {
    styles.borderLeftColor = '#3b82f6'
    styles.borderLeftWidth = '3px'
    styles.borderLeftStyle = 'solid'
  }

  if (analysis.contentType === 'exclamation') {
    styles.borderLeftColor = '#f59e0b'
    styles.borderLeftWidth = '3px'
    styles.borderLeftStyle = 'solid'
  }

  // 根据内容特征调整颜色（仅对AI消息生效）
  if (analysis.hasEmoji && props.message.role !== 'user') {
    styles.background = 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)'
  }

  return styles
})
</script>

<style lang="scss" scoped>
.message-item {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0;
  max-width: 100%;
  padding: 1rem 1.5rem;
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &.user-message {
    justify-content: flex-end;

    .message-content-wrapper {
      max-width: max-content;
      min-width: max-content;
      width: max-content;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background:
        linear-gradient(135deg, #ff6b6b 0%, #ee5a24 20%, #ff9ff3 40%, #54a0ff 60%, #5f27cd 80%, #ff6b6b 100%),
        linear-gradient(45deg, rgba(255,255,255,0.15) 0%, transparent 100%) !important;
      background-size: 300% 300%, 100% 100%;
      background-blend-mode: overlay;
      animation: gradientShift 4s ease-in-out infinite;
      color: white !important;
      padding: 0.875rem 1.125rem;
      border-radius: 20px 20px 6px 20px !important;
      box-shadow:
        0 12px 48px rgba(255, 107, 107, 0.5),
        0 6px 24px rgba(238, 90, 36, 0.4),
        0 3px 12px rgba(255, 159, 243, 0.3),
        0 1px 6px rgba(84, 160, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
      position: relative;
      backdrop-filter: blur(20px) saturate(1.2);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      transform: translateZ(0);
      overflow: hidden;

      // 添加炫酷的悬停效果
      &:hover {
        transform: translateY(-4px) scale(1.05);
        box-shadow:
          0 20px 60px rgba(255, 107, 107, 0.6),
          0 10px 30px rgba(238, 90, 36, 0.5),
          0 5px 18px rgba(255, 159, 243, 0.4),
          0 2px 8px rgba(84, 160, 255, 0.3),
          inset 0 2px 0 rgba(255, 255, 255, 0.5),
          inset 0 -1px 0 rgba(0, 0, 0, 0.15) !important;
        animation-duration: 2s;

        &::before {
          opacity: 1;
          animation-duration: 1.5s;
        }
      }

      // 添加发送动画
      &.sending {
        animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      // 确保所有文本都是白色
      * {
        color: white !important;
      }

      // 动态流光效果
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg,
          transparent 30%,
          rgba(255, 255, 255, 0.3) 50%,
          transparent 70%);
        border-radius: 22px 22px 8px 22px;
        z-index: -1;
        animation: shimmerFlow 3s ease-in-out infinite;
        opacity: 0.6;
      }

      // 气泡尾巴
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: -10px;
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-top: 10px solid #764ba2;
        border-radius: 0 0 0 3px;
        filter: drop-shadow(2px 2px 6px rgba(118, 75, 162, 0.4));
      }

      // 内部粒子效果
      .message-content {
        position: relative;
        z-index: 1;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background:
            radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 40% 60%, rgba(240, 147, 251, 0.1) 0%, transparent 50%);
          border-radius: inherit;
          pointer-events: none;
          animation: particleFloat 4s ease-in-out infinite;
        }
      }

      // 用户消息的动态样式
      &.is-question {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

        &::after {
          content: '❓';
          position: absolute;
          top: -0.5rem;
          right: -0.5rem;
          font-size: 1.2rem;
          background: white;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
      }

      &.is-exclamation {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        animation: excitedPulse 1.5s ease-in-out;

        &::after {
          content: '❗';
          position: absolute;
          top: -0.5rem;
          right: -0.5rem;
          font-size: 1.2rem;
          background: white;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
      }

      &.has-emoji {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        animation: emojiWiggle 2s ease-in-out infinite;

        &::after {
          content: '✨';
          position: absolute;
          top: -0.25rem;
          right: -0.25rem;
          font-size: 0.8rem;
        }
      }

      &.has-code {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        font-family: 'Fira Code', 'JetBrains Mono', monospace;

        &::before {
          content: '</>';
          position: absolute;
          top: 0.5rem;
          right: 0.5rem;
          font-size: 0.75rem;
          opacity: 0.7;
          background: rgba(255, 255, 255, 0.1);
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
        }
      }

      // 动态宽度调整 - 根据内容长度自适应
      &.length-short {
        max-width: max-content;
        min-width: auto;
        width: auto;
        border-radius: 18px 18px 4px 18px;
        padding: 0.75rem 1rem;
        
        .message-content {
          font-size: 0.95rem;
          line-height: 1.4;
          width: auto;
          max-width: none;
        }

        &::after {
          border-left: 8px solid transparent;
          border-top: 8px solid #764ba2;
          right: -8px;
        }
      }

      &.length-medium {
        max-width: max-content;
        min-width: auto;
        width: auto;
        border-radius: 20px 20px 6px 20px;
        padding: 0.875rem 1.125rem;

        .message-content {
          font-size: 0.95rem;
          line-height: 1.5;
          width: auto;
          max-width: 450px;
        }
      }

      &.length-long {
        max-width: max-content;
        min-width: auto;
        width: auto;
        border-radius: 22px 22px 8px 22px;
        padding: 1rem 1.25rem;

        .message-content {
          font-size: 0.94rem;
          line-height: 1.6;
          width: auto;
          max-width: 600px;
        }

        &::after {
          border-left: 12px solid transparent;
          border-top: 12px solid #764ba2;
          right: -12px;
        }
      }


      .message-content {
        color: white !important;
        word-wrap: break-word;
        word-break: break-word;
        hyphens: auto;
        width: auto;
        max-width: none;
        display: inline-block;

        // 确保所有直接文本都是白色
        & {
          color: white !important;
        }

        :deep(p) {
          color: white !important;
          margin: 0;
          font-size: 0.95rem;
          line-height: 1.5;
          word-wrap: break-word;
        }

        // 确保普通文本也是白色
        :deep(span), :deep(div), :deep(text) {
          color: white !important;
        }

        :deep(code) {
          background: rgba(255, 255, 255, 0.2) !important;
          color: white !important;
          border-radius: 4px;
          padding: 0.125rem 0.25rem;
        }

        :deep(pre) {
          background: rgba(0, 0, 0, 0.3) !important;
          color: white !important;
          border-radius: 8px;
          padding: 1rem;
          margin: 0.5rem 0;
          overflow-x: auto;
        }

        :deep(strong) {
          color: white !important;
          font-weight: 600;
        }

        :deep(em) {
          color: rgba(255, 255, 255, 0.9) !important;
        }

        :deep(a) {
          color: rgba(255, 255, 255, 0.9) !important;
          text-decoration: underline;
        }

        :deep(ul), :deep(ol) {
          color: white !important;
          margin: 0.5rem 0;
          padding-left: 1.5rem;
        }

        :deep(li) {
          color: white !important;
          margin: 0.25rem 0;
        }

        :deep(blockquote) {
          border-left: 3px solid rgba(255, 255, 255, 0.3);
          padding-left: 1rem;
          margin: 0.5rem 0;
          color: rgba(255, 255, 255, 0.9) !important;
        }

        :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
          color: white !important;
          margin: 0.5rem 0;
        }
      }
    }
  }

  &.ai-message {
    justify-content: flex-start;

    .message-avatar {
      flex-shrink: 0;
      margin-top: 0.25rem;

      .ai-avatar {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      }
    }

    .message-content-wrapper {
      flex: 1;
      max-width: calc(100% - 48px);

      .message-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;

        .sender-name {
          font-size: 0.875rem;
          font-weight: 600;
          color: #1f2937;
        }

        .message-status {
          &.loading {
            .loading-dots {
              display: flex;
              gap: 0.25rem;

              span {
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background: #6b7280;
                animation: typing 1.4s ease-in-out infinite both;

                &:nth-child(1) { animation-delay: -0.32s; }
                &:nth-child(2) { animation-delay: -0.16s; }
                &:nth-child(3) { animation-delay: 0s; }
              }
            }
          }

          &.error {
            .error-text {
              font-size: 0.75rem;
              color: #ef4444;
            }
          }
        }
      }
    }
  }

  .message-content {
    line-height: 1.6;
    word-wrap: break-word;
    color: #1f2937;
    font-size: 0.95rem;
    background: #ffffff;
    padding: 1.25rem 1.5rem;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f5f9;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // 动态内容类型样式
    &.content-type-code {
      background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
      color: #e2e8f0;
      border-color: #475569;
      font-family: 'Fira Code', 'JetBrains Mono', monospace;

      &::before {
        background: linear-gradient(90deg, transparent 0%, rgba(67, 56, 202, 0.3) 50%, transparent 100%);
      }
    }

    &.content-type-question {
      background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
      border-left: 4px solid #3b82f6;
      border-radius: 4px 18px 18px 4px;

      &::before {
        background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.2) 50%, transparent 100%);
      }
    }

    &.content-type-exclamation {
      background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
      border-left: 4px solid #f59e0b;
      border-radius: 4px 18px 18px 4px;

      &::before {
        background: linear-gradient(90deg, transparent 0%, rgba(245, 158, 11, 0.2) 50%, transparent 100%);
      }
    }

    &.content-type-list {
      background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
      border-left: 3px solid #22c55e;

      &::before {
        background: linear-gradient(90deg, transparent 0%, rgba(34, 197, 94, 0.2) 50%, transparent 100%);
      }
    }

    &.content-type-table {
      background: linear-gradient(135deg, #fafafa 0%, #f4f4f5 100%);
      border: 2px solid #e4e4e7;

      &::before {
        background: linear-gradient(90deg, transparent 0%, rgba(113, 113, 122, 0.2) 50%, transparent 100%);
      }
    }

    &.content-type-quote {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-left: 4px solid #667eea;
      border-radius: 4px 18px 18px 4px;
      font-style: italic;

      &::before {
        background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
      }
    }

    &.content-type-formula {
      background: linear-gradient(135deg, #fdf4ff 0%, #fae8ff 100%);
      border: 2px solid #d946ef;

      &::before {
        background: linear-gradient(90deg, transparent 0%, rgba(217, 70, 239, 0.2) 50%, transparent 100%);
      }
    }

    // 内容长度样式
    &.content-length-short {
      padding: 0.875rem 1.125rem;
      border-radius: 16px;
      font-size: 0.9rem;
    }

    &.content-length-medium {
      padding: 1.125rem 1.375rem;
      border-radius: 18px;
    }

    &.content-length-long {
      padding: 1.5rem 1.75rem;
      border-radius: 20px;
      line-height: 1.7;
    }

    // 特殊效果
    &.animated-content {
      animation: gentlePulse 2s ease-in-out infinite;
    }

    &.code-heavy {
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
      color: #f1f5f9;
      border-color: #334155;
    }

    &.data-rich {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 2px solid #0ea5e9;

      &::after {
        content: '📊';
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        font-size: 1.2rem;
        opacity: 0.6;
      }
    }

    &.interactive {
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.1) 50%, transparent 100%);
      border-radius: 18px 18px 0 0;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.08);
      border-color: rgba(102, 126, 234, 0.2);
    }

    :deep(p) {
      margin: 0 0 0.75rem 0;
      color: #1f2937;
      line-height: 1.6;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
      color: #111827;
      margin: 1rem 0 0.5rem 0;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }
    }

    :deep(ul), :deep(ol) {
      margin: 0.5rem 0;
      padding-left: 1.5rem;
      color: #374151;
    }

    :deep(li) {
      margin: 0.25rem 0;
    }

    :deep(code) {
      background: #f3f4f6;
      color: #374151;
      padding: 0.125rem 0.375rem;
      border-radius: 4px;
      font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
      font-size: 0.9em;
    }

    :deep(.code-block) {
      margin: 1.25rem 0;
      border-radius: 12px;
      overflow: hidden;
      background: #1e293b;
      border: 1px solid #334155;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      .code-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.875rem 1.25rem;
        background: linear-gradient(135deg, #334155 0%, #475569 100%);
        border-bottom: 1px solid #475569;

        .code-lang {
          font-size: 0.75rem;
          font-weight: 600;
          color: #67e8f9;
          text-transform: uppercase;
          background: rgba(103, 232, 249, 0.1);
          padding: 0.25rem 0.5rem;
          border-radius: 6px;
        }

        .copy-code-btn {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.375rem 0.75rem;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          color: #e2e8f0;
          font-size: 0.75rem;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          }
        }
      }

      pre {
        margin: 0;
        padding: 1.25rem;
        overflow-x: auto;
        background: transparent;

        code {
          background: transparent;
          color: #e2e8f0;
          padding: 0;
          font-size: 0.9rem;
          line-height: 1.6;
          font-family: 'Fira Code', 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
        }
      }
    }

    :deep(blockquote) {
      margin: 1.25rem 0;
      padding: 1rem 1.25rem;
      border-left: 4px solid #667eea;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: 0 12px 12px 0;
      color: #475569;
      position: relative;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);

      &::before {
        content: '"';
        position: absolute;
        top: -0.5rem;
        left: 1rem;
        font-size: 2rem;
        color: #667eea;
        font-weight: bold;
        background: #f8fafc;
        padding: 0 0.25rem;
      }
    }

    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 1rem 0;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #e5e7eb;

      th, td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #f3f4f6;
        color: #374151;
      }

      th {
        background: #f9fafb;
        font-weight: 600;
      }

      tr:last-child td {
        border-bottom: none;
      }
    }
  }

  .message-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.75rem;
    padding: 0.5rem 1.25rem 0;

    .action-buttons {
      display: flex;
      gap: 0.25rem;

      .action-btn {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #64748b;

        &:hover {
          background: #f1f5f9;
          color: #475569;
          border-color: #cbd5e1;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .message-time {
      font-size: 0.75rem;
      color: #94a3b8;
      font-weight: 500;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes gentlePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.08);
  }
}

@keyframes excitedPulse {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.05);
  }
  50% {
    transform: scale(1.02);
  }
  75% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes emojiWiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(1deg);
  }
  75% {
    transform: rotate(-1deg);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

// 为不同角色的消息添加不同的入场动画
.message-item {
  &.user-message {
    animation: slideInFromRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &.ai-message {
    animation: slideInFromLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  // 特殊内容类型的额外效果
  &.content-question {
    .message-content {
      position: relative;

      &::after {
        content: '🤔';
        position: absolute;
        top: -0.5rem;
        right: -0.5rem;
        font-size: 1.5rem;
        animation: gentleBounce 2s ease-in-out infinite;
      }
    }
  }

  &.content-exclamation {
    .message-content {
      position: relative;

      &::after {
        content: '✨';
        position: absolute;
        top: -0.5rem;
        right: -0.5rem;
        font-size: 1.5rem;
        animation: sparkle 1.5s ease-in-out infinite;
      }
    }
  }

  &.has-emoji {
    .message-content {
      animation: emojiGlow 3s ease-in-out infinite;
    }
  }
}

@keyframes gentleBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
}

@keyframes emojiGlow {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3), 0 2px 10px rgba(118, 75, 162, 0.2);
  }
}

// 移动端响应式优化
@media (max-width: 768px) {
  .message-item {
    padding: 0.75rem 1rem; // 减少外边距
    margin-bottom: 1rem;
    gap: 0.5rem;

    &.user-message {
      padding-right: 0.75rem;

      .message-content-wrapper {
        max-width: calc(100vw - 3rem);
        min-width: auto;
        width: auto;
        
        // 用户消息气泡优化
        padding: 0.75rem 1rem;
        border-radius: 18px 18px 6px 18px;
        font-size: 0.9rem;
        line-height: 1.4;
        word-wrap: break-word;
        overflow-wrap: break-word;
        
        // 移动端用户消息背景优化
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3), 0 1px 3px rgba(29, 78, 216, 0.2);
        
        // 移除复杂动画效果
        animation: none;
        transform: none;
        
        &:hover {
          transform: none;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3), 0 1px 3px rgba(29, 78, 216, 0.2);
        }

        &::before,
        &::after {
          display: none; // 移除装饰元素
        }

        .message-content {
          color: white !important;
          font-size: 0.9rem;
          line-height: 1.4;
          
          * {
            color: white !important;
          }
        }

        // 长度适配
        &.length-short {
          max-width: 80%;
          padding: 0.625rem 0.875rem;
          border-radius: 16px 16px 4px 16px;
        }

        &.length-medium {
          max-width: 85%;
          padding: 0.75rem 1rem;
        }

        &.length-long {
          max-width: 90%;
          padding: 0.875rem 1.125rem;
        }
      }
    }

    &.ai-message {
      padding-left: 0.75rem;

      .message-avatar {
        margin-top: 0.125rem;
        
        .ai-avatar {
          width: 28px;
          height: 28px;
          box-shadow: 0 1px 3px rgba(102, 126, 234, 0.2);
        }
      }

      .message-content-wrapper {
        max-width: calc(100vw - 4rem);
        
        .message-header {
          margin-bottom: 0.375rem;
          
          .sender-name {
            font-size: 0.8rem;
            font-weight: 600;
            color: #374151;
          }

          .message-status {
            &.loading .loading-dots span {
              width: 3px;
              height: 3px;
            }
          }
        }
      }
    }

    .message-content {
      padding: 0.875rem 1rem;
      font-size: 0.9rem;
      border-radius: 16px;
      line-height: 1.5;
      word-wrap: break-word;
      overflow-wrap: break-word;
      
      // 移除悬停效果
      &:hover {
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      // 内容类型优化
      &.content-length-short {
        padding: 0.75rem 0.875rem;
        font-size: 0.875rem;
      }

      &.content-length-medium {
        padding: 0.875rem 1rem;
      }

      &.content-length-long {
        padding: 1rem 1.125rem;
        line-height: 1.6;
      }

      // 文本内容优化
      :deep(p) {
        margin: 0 0 0.5rem 0;
        line-height: 1.5;
        
        &:last-child {
          margin-bottom: 0;
        }
      }

      :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        margin: 0.75rem 0 0.375rem 0;
        font-size: 1rem;
        
        &:first-child {
          margin-top: 0;
        }
      }

      :deep(ul), :deep(ol) {
        margin: 0.5rem 0;
        padding-left: 1.25rem;
      }

      :deep(li) {
        margin: 0.25rem 0;
        line-height: 1.4;
      }

      :deep(code) {
        font-size: 0.8rem;
        padding: 0.125rem 0.25rem;
        border-radius: 3px;
      }

      :deep(.code-block) {
        margin: 0.75rem 0;
        border-radius: 8px;
        
        .code-header {
          padding: 0.625rem 0.875rem;
          
          .code-lang {
            font-size: 0.7rem;
          }
          
          .copy-code-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.7rem;
          }
        }
        
        pre {
          padding: 0.875rem;
          
          code {
            font-size: 0.8rem;
            line-height: 1.4;
          }
        }
      }

      :deep(blockquote) {
        margin: 0.75rem 0;
        padding: 0.625rem 0.875rem;
        border-radius: 0 8px 8px 0;
        
        &::before {
          font-size: 1.5rem;
          top: -0.25rem;
        }
      }

      :deep(table) {
        font-size: 0.8rem;
        margin: 0.75rem 0;
        
        th, td {
          padding: 0.5rem 0.625rem;
        }
      }
    }

    .message-actions {
      margin-top: 0.5rem;
      padding: 0.375rem 0.875rem 0;
      
      .action-buttons {
        gap: 0.375rem;
        
        .action-btn {
          width: 36px;
          height: 36px;
          border-radius: 8px;
          background: rgba(248, 250, 252, 0.9);
          border: 1px solid rgba(226, 232, 240, 0.8);
          
          &:hover {
            transform: none;
            background: rgba(241, 245, 249, 0.95);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }
          
          svg {
            width: 16px;
            height: 16px;
          }
        }
      }
      
      .message-time {
        font-size: 0.7rem;
        color: #9ca3af;
      }
    }
  }
}

// 超小屏幕优化 (480px以下)
@media (max-width: 480px) {
  .message-item {
    margin-bottom: 0.75rem; // 进一步减少间距

    .message-content {
      padding: 0.75rem 0.875rem; // 更紧凑的内边距
      font-size: 0.875rem;

      :deep(p) {
        margin: 0 0 0.375rem 0;
      }

      :deep(pre) {
        code {
          font-size: 0.75rem; // 更小的代码字体
        }
      }
    }

    &.user-message {
      .message-content {
        max-width: 90%; // 用户消息在超小屏幕上更宽
        padding: 0.625rem 0.875rem;
      }
    }

    .message-actions {
      .action-btn {
        width: 32px; // 超小屏幕上稍小按钮
        height: 32px;
      }
    }

    .message-avatar {
      .avatar-wrapper {
        width: 28px; // 更小头像
        height: 28px;
      }
    }
  }
}

// 用户消息动画效果
@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-2px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// 气泡呼吸动画（可选）
@keyframes bubblePulse {
  0%, 100% {
    box-shadow:
      0 8px 32px rgba(102, 126, 234, 0.3),
      0 4px 16px rgba(118, 75, 162, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(102, 126, 234, 0.4),
      0 6px 20px rgba(118, 75, 162, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
  }
}
// 流光动画
@keyframes shimmerFlow {
  0% {
    transform: translateX(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    transform: translateX(200%) rotate(45deg);
    opacity: 0;
  }
}

// 粒子浮动效果
@keyframes particleFloat {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0px) scale(1);
  }
  33% {
    opacity: 0.6;
    transform: translateY(-2px) scale(1.05);
  }
  66% {
    opacity: 0.4;
    transform: translateY(1px) scale(0.95);
  }
}

// 彩虹边框动画（可选）
@keyframes rainbowBorder {
  0% {
    border-color: rgba(102, 126, 234, 0.5);
  }
  25% {
    border-color: rgba(118, 75, 162, 0.5);
  }
  50% {
    border-color: rgba(240, 147, 251, 0.5);
  }
  75% {
    border-color: rgba(102, 126, 234, 0.5);
  }
  100% {
    border-color: rgba(118, 75, 162, 0.5);
  }
}

// 脉冲发光效果
@keyframes pulseGlow {
  0%, 100% {
    box-shadow:
      0 10px 40px rgba(102, 126, 234, 0.4),
      0 5px 20px rgba(118, 75, 162, 0.3),
      0 2px 10px rgba(240, 147, 251, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow:
      0 15px 50px rgba(102, 126, 234, 0.6),
      0 8px 25px rgba(118, 75, 162, 0.5),
      0 4px 15px rgba(240, 147, 251, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

// 为特殊消息类型添加额外动画
.message-item.user-message {
  .message-content-wrapper {
    // 问题消息的特殊效果
    &.is-question {
      animation: pulseGlow 2s ease-in-out infinite;
      border: 1px solid rgba(59, 130, 246, 0.3);
    }

    // 感叹消息的特殊效果
    &.is-exclamation {
      animation: rainbowBorder 3s linear infinite;
    }

    // 包含代码的消息
    &.has-code {
      &::before {
        background: linear-gradient(45deg,
          rgba(0, 255, 255, 0.2) 0%,
          rgba(255, 0, 255, 0.2) 50%,
          rgba(255, 255, 0, 0.2) 100%);
        animation: shimmerFlow 2s ease-in-out infinite;
      }
    }
  }
}

// QQ风格炫酷动画
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes bubbleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-2px) rotate(0.5deg);
  }
  66% {
    transform: translateY(1px) rotate(-0.5deg);
  }
}

@keyframes magicSparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

@keyframes rainbowGlow {
  0% { filter: hue-rotate(0deg) brightness(1); }
  25% { filter: hue-rotate(90deg) brightness(1.1); }
  50% { filter: hue-rotate(180deg) brightness(1.2); }
  75% { filter: hue-rotate(270deg) brightness(1.1); }
  100% { filter: hue-rotate(360deg) brightness(1); }
}

// 为用户消息添加额外的炫酷效果
.message-item.user-message {
  .message-content-wrapper {
    // 添加随机闪烁的小星星效果
    &::after {
      content: '✨';
      position: absolute;
      top: -8px;
      right: -8px;
      font-size: 16px;
      animation: magicSparkle 3s ease-in-out infinite;
      z-index: 10;
    }

    // 特殊节日效果（可选）
    &.celebration {
      animation: rainbowGlow 2s ease-in-out infinite;

      &::before {
        content: '🎉';
        position: absolute;
        top: -12px;
        left: -12px;
        font-size: 20px;
        animation: magicSparkle 2s ease-in-out infinite;
        z-index: 10;
      }
    }
  }
}
</style>

// 超小屏幕优化 (480px以下)
@media (max-width: 480px) {
  .message-item {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.75rem;
    gap: 0.375rem;

    &.user-message {
      padding-right: 0.5rem;

      .message-content-wrapper {
        max-width: calc(100vw - 2.5rem);
        padding: 0.625rem 0.875rem;
        font-size: 0.875rem;
        border-radius: 16px 16px 4px 16px;

        &.length-short {
          max-width: 85%;
          padding: 0.5rem 0.75rem;
          font-size: 0.85rem;
        }

        &.length-medium {
          max-width: 90%;
          padding: 0.625rem 0.875rem;
        }

        &.length-long {
          max-width: 95%;
          padding: 0.75rem 1rem;
        }

        .message-content {
          font-size: 0.875rem;
          line-height: 1.4;
        }
      }
    }

    &.ai-message {
      padding-left: 0.5rem;

      .message-avatar {
        .ai-avatar {
          width: 24px;
          height: 24px;
        }
      }

      .message-content-wrapper {
        max-width: calc(100vw - 3rem);

        .message-header {
          margin-bottom: 0.25rem;

          .sender-name {
            font-size: 0.75rem;
          }

          .message-status.loading .loading-dots span {
            width: 2px;
            height: 2px;
          }
        }
      }
    }

    .message-content {
      padding: 0.75rem 0.875rem;
      font-size: 0.875rem;
      border-radius: 14px;

      &.content-length-short {
        padding: 0.625rem 0.75rem;
        font-size: 0.85rem;
      }

      &.content-length-medium {
        padding: 0.75rem 0.875rem;
        font-size: 0.875rem;
      }

      &.content-length-long {
        padding: 0.875rem 1rem;
        font-size: 0.875rem;
        line-height: 1.5;
      }

      :deep(p) {
        margin: 0 0 0.375rem 0;
        line-height: 1.4;
      }

      :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        font-size: 0.95rem;
        margin: 0.5rem 0 0.25rem 0;
      }

      :deep(code) {
        font-size: 0.75rem;
        padding: 0.1rem 0.2rem;
      }

      :deep(.code-block) {
        margin: 0.5rem 0;
        border-radius: 6px;

        .code-header {
          padding: 0.5rem 0.75rem;

          .code-lang {
            font-size: 0.65rem;
            padding: 0.2rem 0.4rem;
          }

          .copy-code-btn {
            padding: 0.2rem 0.4rem;
            font-size: 0.65rem;
          }
        }

        pre {
          padding: 0.75rem;

          code {
            font-size: 0.75rem;
            line-height: 1.3;
          }
        }
      }

      :deep(blockquote) {
        margin: 0.5rem 0;
        padding: 0.5rem 0.75rem;
        border-radius: 0 6px 6px 0;

        &::before {
          font-size: 1.25rem;
          top: -0.2rem;
        }
      }

      :deep(table) {
        font-size: 0.75rem;
        margin: 0.5rem 0;

        th, td {
          padding: 0.375rem 0.5rem;
        }
      }

      :deep(ul), :deep(ol) {
        margin: 0.375rem 0;
        padding-left: 1rem;
      }

      :deep(li) {
        margin: 0.2rem 0;
        line-height: 1.3;
      }
    }

    .message-actions {
      margin-top: 0.375rem;
      padding: 0.25rem 0.75rem 0;

      .action-buttons {
        gap: 0.25rem;

        .action-btn {
          width: 32px;
          height: 32px;
          border-radius: 6px;

          svg {
            width: 14px;
            height: 14px;
          }
        }
      }

      .message-time {
        font-size: 0.65rem;
      }
    }
  }
}

// 极小屏幕优化 (360px以下)
@media (max-width: 360px) {
  .message-item {
    padding: 0.375rem 0.5rem;
    margin-bottom: 0.5rem;

    &.user-message {
      .message-content-wrapper {
        max-width: calc(100vw - 2rem);
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;

        &.length-short {
          padding: 0.375rem 0.625rem;
          font-size: 0.8rem;
        }
      }
    }

    &.ai-message {
      .message-avatar {
        .ai-avatar {
          width: 20px;
          height: 20px;
        }
      }

      .message-content-wrapper {
        max-width: calc(100vw - 2.5rem);

        .message-header {
          .sender-name {
            font-size: 0.7rem;
          }
        }
      }
    }

    .message-content {
      padding: 0.625rem 0.75rem;
      font-size: 0.85rem;
      border-radius: 12px;

      &.content-length-short {
        padding: 0.5rem 0.625rem;
        font-size: 0.8rem;
      }

      :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        font-size: 0.9rem;
      }

      :deep(.code-block) {
        .code-header {
          padding: 0.375rem 0.5rem;

          .code-lang {
            font-size: 0.6rem;
          }

          .copy-code-btn {
            font-size: 0.6rem;
            padding: 0.15rem 0.3rem;
          }
        }

        pre {
          padding: 0.5rem;

          code {
            font-size: 0.7rem;
          }
        }
      }
    }

    .message-actions {
      padding: 0.2rem 0.5rem 0;

      .action-buttons {
        .action-btn {
          width: 28px;
          height: 28px;

          svg {
            width: 12px;
            height: 12px;
          }
        }
      }

      .message-time {
        font-size: 0.6rem;
      }
    }
  }
}
