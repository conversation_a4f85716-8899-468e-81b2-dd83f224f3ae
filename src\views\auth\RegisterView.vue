<template>
  <div class="register-view">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <div class="register-container">
      <!-- 左侧注册表单 -->
      <div class="register-card">
        <!-- 返回登录按钮 -->
        <div class="back-login">
          <button @click="goToLogin" class="back-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            返回登录
          </button>
        </div>

        <div class="register-header">
          <div class="logo-section">
            <div class="logo-icon">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                <path d="M20 8v6M23 11l-3 3-3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h1>创建账户</h1>
            <p class="subtitle">加入AI创作助手平台，开启您的创作之旅</p>
          </div>
        </div>

        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="register-form"
          @submit.prevent="handleRegister"
        >
          <div class="form-group">
            <label class="form-label">用户名</label>
            <el-form-item prop="username">
              <el-input
                v-model="registerForm.username"
                placeholder="请输入用户名"
                size="large"
                :prefix-icon="User"
                clearable
                class="custom-input"
              />
            </el-form-item>
          </div>

          <div class="form-group">
            <label class="form-label">邮箱地址</label>
            <el-form-item prop="email">
              <el-input
                v-model="registerForm.email"
                placeholder="请输入邮箱地址"
                size="large"
                :prefix-icon="Message"
                clearable
                class="custom-input"
              />
            </el-form-item>
          </div>

          <div class="form-group">
            <label class="form-label">密码</label>
            <el-form-item prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                :prefix-icon="Lock"
                show-password
                clearable
                class="custom-input"
              />
            </el-form-item>
          </div>

          <div class="form-group">
            <label class="form-label">确认密码</label>
            <el-form-item prop="confirmPassword">
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="请确认密码"
                size="large"
                :prefix-icon="Lock"
                show-password
                clearable
                class="custom-input"
              />
            </el-form-item>
          </div>

          <div class="agreement-section">
            <el-form-item prop="agreement">
              <el-checkbox v-model="registerForm.agreement" class="custom-checkbox">
                我已阅读并同意
                <button type="button" @click="showTerms" class="link-btn">《用户协议》</button>
                和
                <button type="button" @click="showPrivacy" class="link-btn">《隐私政策》</button>
              </el-checkbox>
            </el-form-item>
          </div>

          <button
            type="button"
            :disabled="loading"
            @click="handleRegister"
            class="register-btn"
          >
            <span v-if="loading" class="loading-spinner"></span>
            {{ loading ? '注册中...' : '创建账户' }}
          </button>

          <div class="login-link">
            <span>已有账户？</span>
            <button type="button" @click="goToLogin" class="login-btn-link">
              立即登录
            </button>
          </div>
        </el-form>
      </div>

      <!-- 右侧展示区域 -->
      <div class="showcase-section">
        <!-- 动态粒子背景 -->
        <div class="particles-container">
          <div class="particle" v-for="n in 20" :key="n" :style="getParticleStyle(n)"></div>
        </div>

        <!-- 3D几何装饰 -->
        <div class="geometric-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
          <div class="shape shape-4"></div>
        </div>

        <div class="showcase-content">
          <!-- 主标题区域 -->
          <div class="showcase-header">
            <div class="title-container">
              <div class="title-decoration"></div>
              <h2 class="animated-title">
                <span class="title-line">加入创作者</span>
                <span class="title-line highlight">社区</span>
              </h2>
              <div class="subtitle-container">
                <p class="animated-subtitle">与全球创作者一起探索AI的无限可能</p>
                <div class="subtitle-decoration"></div>
              </div>
            </div>
          </div>

          <!-- 特性展示卡片 -->
          <div class="features-grid">
            <div class="feature-card" v-for="(feature, index) in features" :key="index" :style="{ animationDelay: `${index * 0.2}s` }">
              <div class="feature-icon-container">
                <div class="feature-icon" v-html="feature.icon"></div>
                <div class="icon-glow"></div>
              </div>
              <div class="feature-content">
                <h3>{{ feature.title }}</h3>
                <p>{{ feature.description }}</p>
              </div>
              <div class="card-shine"></div>
            </div>
          </div>

          <!-- 统计数据展示 -->
          <div class="stats-section">
            <div class="stats-container">
              <div class="stat-item" v-for="(stat, index) in stats" :key="index" :style="{ animationDelay: `${index * 0.1}s` }">
                <div class="stat-icon">{{ stat.icon }}</div>
                <div class="stat-number">{{ stat.number }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>

          <!-- 实时数据展示 -->
          <div class="realtime-section">
            <div class="realtime-header">
              <h3>
                <span class="pulse-dot"></span>
                实时数据
              </h3>
            </div>
            <div class="realtime-grid">
              <div class="realtime-item">
                <div class="realtime-icon">👥</div>
                <div class="realtime-content">
                  <div class="realtime-number">{{ realTimeData.onlineUsers.toLocaleString() }}</div>
                  <div class="realtime-label">在线用户</div>
                </div>
              </div>
              <div class="realtime-item">
                <div class="realtime-icon">🎨</div>
                <div class="realtime-content">
                  <div class="realtime-number">{{ realTimeData.todayCreations }}</div>
                  <div class="realtime-label">今日创作</div>
                </div>
              </div>
              <div class="realtime-item">
                <div class="realtime-icon">⚡</div>
                <div class="realtime-content">
                  <div class="realtime-number">{{ realTimeData.processingTasks }}</div>
                  <div class="realtime-label">处理中</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 技术栈展示 -->
          <div class="tech-stack-section">
            <h3 class="section-title">核心技术</h3>
            <div class="tech-stack-grid">
              <div class="tech-item" v-for="(tech, index) in techStack" :key="index" :style="{ animationDelay: `${index * 0.15}s` }">
                <div class="tech-icon" :style="{ color: tech.color }">{{ tech.icon }}</div>
                <div class="tech-name">{{ tech.name }}</div>
              </div>
            </div>
          </div>

          <!-- 用户评价轮播 -->
          <div class="testimonials-carousel">
            <div class="carousel-header">
              <h3 class="section-title">用户评价</h3>
              <div class="carousel-controls">
                <button @click="prevTestimonial" class="carousel-btn prev-btn">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M15 18l-6-6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
                <button @click="nextTestimonial" class="carousel-btn next-btn">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>

            <div class="testimonials-container" @mouseenter="stopTestimonialAutoplay" @mouseleave="startTestimonialAutoplay">
              <div
                class="testimonial-card"
                v-for="(testimonial, index) in testimonials"
                :key="testimonial.id"
                :class="{ active: index === currentTestimonialIndex }"
                :style="{ transform: `translateX(${(index - currentTestimonialIndex) * 100}%)` }"
              >
                <div class="testimonial-content">
                  <div class="quote-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z" fill="currentColor"/>
                      <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z" fill="currentColor"/>
                    </svg>
                  </div>
                  <p class="testimonial-text">{{ testimonial.text }}</p>
                  <div class="testimonial-author">
                    <div class="author-avatar" :style="{ backgroundColor: testimonial.color }">
                      {{ testimonial.avatar }}
                    </div>
                    <div class="author-info">
                      <span class="author-name">{{ testimonial.author }}</span>
                      <span class="author-title">{{ testimonial.title }}</span>
                      <div class="rating">
                        <span class="star" v-for="n in testimonial.rating" :key="n">★</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 轮播指示器 -->
            <div class="carousel-indicators">
              <button
                v-for="(testimonial, index) in testimonials"
                :key="index"
                @click="currentTestimonialIndex = index"
                class="indicator"
                :class="{ active: index === currentTestimonialIndex }"
              ></button>
            </div>
          </div>

          <!-- 加入社区CTA -->
          <div class="cta-section">
            <div class="cta-content">
              <div class="cta-icon">🚀</div>
              <h3>准备好开始创作了吗？</h3>
              <p>加入我们的创作者社区，释放你的创意潜能</p>
              <div class="cta-features">
                <span class="cta-feature">✨ 免费开始</span>
                <span class="cta-feature">🎯 专业工具</span>
                <span class="cta-feature">🤝 社区支持</span>
              </div>
            </div>
          </div>

          <!-- 底部装饰 -->
          <div class="bottom-decoration">
            <div class="wave-animation"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Message } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'

const router = useRouter()
const userStore = useUserStore()
const registerFormRef = ref()
const loading = ref(false)

// 注册表单数据
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreement: false
})

// 特性数据
const features = ref([
  {
    title: '免费使用',
    description: '享受基础AI功能，无需付费',
    icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
    </svg>`
  },
  {
    title: '快速创作',
    description: 'AI助力，让创作更高效',
    icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`
  },
  {
    title: '社区分享',
    description: '与创作者交流，分享作品',
    icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
      <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
      <path d="M23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2"/>
    </svg>`
  },
  {
    title: '持续更新',
    description: '不断优化，功能持续增强',
    icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
    </svg>`
  }
])

// 统计数据
const stats = ref([
  { number: '10K+', label: '活跃用户', icon: '👥' },
  { number: '50K+', label: '创作作品', icon: '🎨' },
  { number: '99%', label: '满意度', icon: '⭐' },
  { number: '24/7', label: '在线服务', icon: '🚀' }
])

// 用户评价数据
const testimonials = ref([
  {
    id: 1,
    text: "这个平台让我的创作效率提升了300%，AI助手真的很棒！",
    author: "张小明",
    title: "数字艺术家",
    avatar: "张",
    rating: 5,
    color: "#667eea"
  },
  {
    id: 2,
    text: "界面设计很棒，功能强大且易用，强烈推荐给所有创作者！",
    author: "李美丽",
    title: "UI设计师",
    avatar: "李",
    rating: 5,
    color: "#764ba2"
  },
  {
    id: 3,
    text: "AI创作功能超出预期，帮我节省了大量时间，作品质量也提升了。",
    author: "王创新",
    title: "内容创作者",
    avatar: "王",
    rating: 5,
    color: "#f093fb"
  }
])

// 当前显示的评价索引
const currentTestimonialIndex = ref(0)

// 技术栈展示
const techStack = ref([
  { name: 'Vue 3', icon: '⚡', color: '#4FC08D' },
  { name: 'AI Engine', icon: '🧠', color: '#FF6B6B' },
  { name: 'Cloud', icon: '☁️', color: '#4ECDC4' },
  { name: 'Security', icon: '🔒', color: '#45B7D1' }
])

// 实时数据（模拟）
const realTimeData = ref({
  onlineUsers: 1247,
  todayCreations: 89,
  processingTasks: 23
})

// 生成粒子样式
const getParticleStyle = (index) => {
  const size = Math.random() * 4 + 2
  const left = Math.random() * 100
  const animationDuration = Math.random() * 20 + 10
  const animationDelay = Math.random() * 5

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    animationDuration: `${animationDuration}s`,
    animationDelay: `${animationDelay}s`
  }
}

// 评价轮播逻辑
const nextTestimonial = () => {
  currentTestimonialIndex.value = (currentTestimonialIndex.value + 1) % testimonials.value.length
}

const prevTestimonial = () => {
  currentTestimonialIndex.value = currentTestimonialIndex.value === 0
    ? testimonials.value.length - 1
    : currentTestimonialIndex.value - 1
}

// 自动轮播
let testimonialInterval = null

const startTestimonialAutoplay = () => {
  testimonialInterval = setInterval(nextTestimonial, 5000)
}

const stopTestimonialAutoplay = () => {
  if (testimonialInterval) {
    clearInterval(testimonialInterval)
    testimonialInterval = null
  }
}

// 组件挂载时启动自动轮播

onMounted(() => {
  startTestimonialAutoplay()
  // 模拟实时数据更新
  setInterval(() => {
    realTimeData.value.onlineUsers += Math.floor(Math.random() * 10) - 5
    realTimeData.value.todayCreations += Math.floor(Math.random() * 3)
    realTimeData.value.processingTasks = Math.floor(Math.random() * 50) + 10
  }, 3000)
})

onUnmounted(() => {
  stopTestimonialAutoplay()
})

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 1, max: 20, message: '用户名长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  agreement: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请阅读并同意用户协议和隐私政策'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 处理注册
const handleRegister = async () => {
  try {
    await registerFormRef.value.validate()
    loading.value = true

    // 调用用户store的注册方法
    const result = await userStore.register({
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password
    })

    if (result.success) {
      // 注册成功，跳转到登录页
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  } catch (error) {
    console.log('表单验证失败:', error)
    ElMessage({
      message: '请检查表单信息是否正确',
      type: 'warning',
      duration: 3000,
      showClose: true,
      customClass: 'high-contrast-message',
      center: true
    })
  } finally {
    loading.value = false
  }
}

// 跳转到登录页
const goToLogin = () => {
  router.push('/login')
}

// 显示用户协议
const showTerms = () => {
  router.push({
    path: '/terms',
    query: { from: '/register' }
  })
}

// 显示隐私政策
const showPrivacy = () => {
  router.push({
    path: '/privacy',
    query: { from: '/register' }
  })
}
</script>

<style lang="scss" scoped>
.register-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;

  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;

    &.circle-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.circle-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }

    &.circle-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

// 新增动画关键帧
@keyframes backgroundShift {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes shapeFloat1 {
  0%, 100% { transform: rotate(45deg) translateY(0px); }
  50% { transform: rotate(45deg) translateY(-20px); }
}

@keyframes shapeFloat2 {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-15px) scale(1.1); }
}

@keyframes shapeFloat3 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-25px) rotate(180deg); }
}

@keyframes shapeFloat4 {
  0%, 100% { transform: rotate(-15deg) translateY(0px); }
  50% { transform: rotate(-15deg) translateY(-18px); }
}

@keyframes titleDecorationGlow {
  0%, 100% { opacity: 0.5; transform: translateX(-50%) scaleX(1); }
  50% { opacity: 1; transform: translateX(-50%) scaleX(1.2); }
}

@keyframes titleSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes highlightGlow {
  0%, 100% { opacity: 0.5; transform: scaleX(1); }
  50% { opacity: 1; transform: scaleX(1.1); }
}

@keyframes subtitleFadeIn {
  to {
    opacity: 0.9;
    transform: translateY(0);
  }
}

@keyframes subtitleDecorationExpand {
  to {
    transform: translateX(-50%) scaleX(1);
  }
}

@keyframes cardSlideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes statsShine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

@keyframes numberPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes waveFlow {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

@keyframes statItemSlideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes iconBounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes techItemSlideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes techIconFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-3px) rotate(5deg); }
}

@keyframes ctaRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes ctaIconBounce {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.1); }
}

.register-container {
  display: flex;
  min-height: 100vh;
  position: relative;
  z-index: 2;
}

.register-card {
  flex: 1;
  max-width: 500px;
  padding: 3rem 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);

  .back-login {
    position: absolute;
    top: 2rem;
    left: 2rem;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 50px;
      color: #666;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: white;
        transform: translateX(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      svg {
        transition: transform 0.3s ease;
      }

      &:hover svg {
        transform: translateX(-2px);
      }
    }
  }

  .register-header {
    text-align: center;
    margin-bottom: 2.5rem;

    .logo-section {
      margin-bottom: 2rem;

      .logo-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        margin-bottom: 1.5rem;
        color: white;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
      }

      h1 {
        margin: 0 0 0.5rem 0;
        font-size: 2.2rem;
        font-weight: 700;
        color: #2d3748;
        letter-spacing: -0.02em;
      }

      .subtitle {
        margin: 0;
        color: #718096;
        font-size: 1rem;
        line-height: 1.5;
      }
    }
  }

  .register-form {
    .form-group {
      margin-bottom: 1.5rem;

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #4a5568;
        font-size: 0.9rem;
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 0;

      .el-form-item__error {
        font-size: 0.8rem;
        margin-top: 0.25rem;
      }
    }

    :deep(.custom-input) {
      .el-input__wrapper {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        box-shadow: none;

        &:hover {
          border-color: #cbd5e0;
          background: white;
        }

        &.is-focus {
          border-color: #667eea;
          background: white;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
      }

      .el-input__inner {
        font-size: 1rem;
        color: #2d3748;

        &::placeholder {
          color: #a0aec0;
        }
      }
    }

    .agreement-section {
      margin: 2rem 0;

      :deep(.custom-checkbox) {
        .el-checkbox__label {
          color: #4a5568;
          font-size: 0.9rem;
          line-height: 1.5;
        }
      }

      .link-btn {
        background: none;
        border: none;
        color: #667eea;
        font-size: 0.9rem;
        cursor: pointer;
        transition: color 0.3s ease;
        text-decoration: underline;

        &:hover {
          color: #5a67d8;
        }
      }
    }

    .register-btn {
      width: 100%;
      height: 52px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      color: white;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin: 2rem 0 1.5rem 0;
      position: relative;
      overflow: hidden;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }

      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
        margin-right: 0.5rem;
      }
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .login-link {
      text-align: center;
      color: #718096;
      font-size: 0.9rem;

      .login-btn-link {
        background: none;
        border: none;
        color: #667eea;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        margin-left: 0.25rem;
        transition: color 0.3s ease;

        &:hover {
          color: #5a67d8;
          text-decoration: underline;
        }
      }
    }
  }
}

.showcase-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #5a67d8 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    animation: backgroundShift 10s ease-in-out infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.03"><circle cx="30" cy="30" r="1.5"/></g></svg>');
    opacity: 0.4;
  }

  // 粒子背景
  .particles-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;

    .particle {
      position: absolute;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      animation: particleFloat linear infinite;
    }
  }

  // 3D几何装饰
  .geometric-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);

      &.shape-1 {
        width: 100px;
        height: 100px;
        top: 10%;
        right: 10%;
        border-radius: 20px;
        transform: rotate(45deg);
        animation: shapeFloat1 8s ease-in-out infinite;
      }

      &.shape-2 {
        width: 60px;
        height: 60px;
        bottom: 20%;
        left: 15%;
        border-radius: 50%;
        animation: shapeFloat2 6s ease-in-out infinite;
      }

      &.shape-3 {
        width: 80px;
        height: 80px;
        top: 50%;
        right: 20%;
        clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        animation: shapeFloat3 10s ease-in-out infinite;
      }

      &.shape-4 {
        width: 120px;
        height: 40px;
        bottom: 30%;
        right: 30%;
        border-radius: 20px;
        transform: rotate(-15deg);
        animation: shapeFloat4 7s ease-in-out infinite;
      }
    }
  }

  .showcase-content {
    text-align: center;
    max-width: 550px;
    position: relative;
    z-index: 2;

    .showcase-header {
      margin-bottom: 3rem;

      .title-container {
        position: relative;

        .title-decoration {
          position: absolute;
          top: -20px;
          left: 50%;
          transform: translateX(-50%);
          width: 60px;
          height: 4px;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
          border-radius: 2px;
          animation: titleDecorationGlow 2s ease-in-out infinite;
        }

        .animated-title {
          font-size: 2.8rem;
          font-weight: 900;
          margin-bottom: 1.5rem;
          line-height: 1.1;
          letter-spacing: -0.02em;
          position: relative;
          overflow: hidden;

          .title-line {
            display: block;
            background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: titleSlideIn 1s ease-out forwards;
            opacity: 0;
            transform: translateY(50px);

            &:nth-child(1) {
              animation-delay: 0.2s;
            }

            &:nth-child(2) {
              animation-delay: 0.4s;
            }

            &.highlight {
              background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #fff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              position: relative;

              &::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, transparent, #ffd700, transparent);
                border-radius: 2px;
                animation: highlightGlow 2s ease-in-out infinite;
              }
            }
          }
        }

        .subtitle-container {
          position: relative;

          .animated-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.6;
            margin: 0;
            animation: subtitleFadeIn 1s ease-out 0.8s forwards;
            opacity: 0;
            transform: translateY(20px);
          }

          .subtitle-decoration {
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
            border-radius: 1px;
            animation: subtitleDecorationExpand 1s ease-out 1s forwards;
            transform: translateX(-50%) scaleX(0);
          }
        }
      }
    }

    // 特性网格
    .features-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
      margin-bottom: 3rem;

      .feature-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 1.5rem;
        text-align: left;
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        animation: cardSlideUp 0.8s ease-out forwards;
        opacity: 0;
        transform: translateY(30px);

        &:hover {
          transform: translateY(-8px) scale(1.02);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
          border-color: rgba(255, 255, 255, 0.4);

          .feature-icon-container {
            transform: scale(1.1) rotate(5deg);
          }

          .card-shine {
            opacity: 1;
            transform: translateX(100%);
          }

          .icon-glow {
            opacity: 1;
            transform: scale(1.5);
          }
        }

        .feature-icon-container {
          position: relative;
          width: 56px;
          height: 56px;
          margin-bottom: 1rem;
          transition: all 0.4s ease;

          .feature-icon {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            z-index: 2;
          }

          .icon-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            opacity: 0;
            transition: all 0.4s ease;
          }
        }

        .feature-content {
          h3 {
            font-size: 1.1rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            color: white;
            letter-spacing: -0.01em;
          }

          p {
            font-size: 0.9rem;
            opacity: 0.85;
            margin: 0;
            line-height: 1.5;
          }
        }

        .card-shine {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          opacity: 0;
          transition: all 0.6s ease;
          pointer-events: none;
        }
      }
    }

    // 统计数据
    .stats-section {
      margin-bottom: 2.5rem;

      .stats-container {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          animation: statsShine 3s ease-in-out infinite;
        }

        .stat-item {
          text-align: center;
          position: relative;
          animation: statItemSlideUp 0.8s ease-out forwards;
          opacity: 0;
          transform: translateY(20px);

          .stat-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            animation: iconBounce 2s ease-in-out infinite;
          }

          .stat-number {
            font-size: 1.8rem;
            font-weight: 900;
            color: #ffd700;
            margin-bottom: 0.25rem;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            animation: numberPulse 2s ease-in-out infinite;
          }

          .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
            font-weight: 500;
            letter-spacing: 0.5px;
          }
        }
      }
    }

    // 实时数据
    .realtime-section {
      margin-bottom: 2.5rem;

      .realtime-header {
        margin-bottom: 1rem;

        h3 {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 1.1rem;
          font-weight: 600;
          color: white;
          margin: 0;

          .pulse-dot {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
            box-shadow: 0 0 10px #00ff88;
          }
        }
      }

      .realtime-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;

        .realtime-item {
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-radius: 12px;
          padding: 1rem;
          display: flex;
          align-items: center;
          gap: 0.75rem;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateY(-2px);
          }

          .realtime-icon {
            font-size: 1.2rem;
            opacity: 0.8;
          }

          .realtime-content {
            .realtime-number {
              font-size: 1.1rem;
              font-weight: 700;
              color: #00ff88;
              margin-bottom: 0.1rem;
              text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
            }

            .realtime-label {
              font-size: 0.7rem;
              opacity: 0.7;
              font-weight: 500;
            }
          }
        }
      }
    }

    // 技术栈
    .tech-stack-section {
      margin-bottom: 2.5rem;

      .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
        margin: 0 0 1rem 0;
        text-align: center;
      }

      .tech-stack-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;

        .tech-item {
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-radius: 12px;
          padding: 1rem;
          text-align: center;
          transition: all 0.4s ease;
          animation: techItemSlideUp 0.8s ease-out forwards;
          opacity: 0;
          transform: translateY(20px);

          &:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
          }

          .tech-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            display: block;
            animation: techIconFloat 3s ease-in-out infinite;
          }

          .tech-name {
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
            opacity: 0.9;
          }
        }
      }
    }

    // 评价轮播
    .testimonials-carousel {
      margin-bottom: 2.5rem;

      .carousel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        .section-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: white;
          margin: 0;
        }

        .carousel-controls {
          display: flex;
          gap: 0.5rem;

          .carousel-btn {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              transform: scale(1.1);
            }

            &:active {
              transform: scale(0.95);
            }
          }
        }
      }

      .testimonials-container {
        position: relative;
        height: 200px;
        overflow: hidden;
        border-radius: 20px;

        .testimonial-card {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 20px;
          padding: 1.5rem;
          transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
          opacity: 0;

          &.active {
            opacity: 1;
          }

          .testimonial-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .quote-icon {
              position: absolute;
              top: 1rem;
              right: 1rem;
              opacity: 0.3;
              color: #ffd700;
            }

            .testimonial-text {
              font-size: 1rem;
              font-style: italic;
              line-height: 1.6;
              opacity: 0.95;
              margin-bottom: 1rem;
              flex: 1;
            }

            .testimonial-author {
              display: flex;
              align-items: center;
              gap: 1rem;

              .author-avatar {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: bold;
                font-size: 1.1rem;
                border: 2px solid rgba(255, 255, 255, 0.3);
              }

              .author-info {
                text-align: left;

                .author-name {
                  display: block;
                  font-weight: 700;
                  font-size: 0.9rem;
                  margin-bottom: 0.2rem;
                  color: white;
                }

                .author-title {
                  font-size: 0.8rem;
                  opacity: 0.8;
                  margin-bottom: 0.3rem;
                }

                .rating {
                  .star {
                    color: #ffd700;
                    font-size: 0.8rem;
                    margin-right: 1px;
                    text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
                  }
                }
              }
            }
          }
        }
      }

      .carousel-indicators {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1rem;

        .indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.3);
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;

          &.active {
            background: #ffd700;
            transform: scale(1.2);
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
          }

          &:hover:not(.active) {
            background: rgba(255, 255, 255, 0.5);
            transform: scale(1.1);
          }
        }
      }
    }

    // CTA部分
    .cta-section {
      .cta-content {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        padding: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          animation: ctaRotate 8s linear infinite;
          pointer-events: none;
        }

        .cta-icon {
          font-size: 2.5rem;
          margin-bottom: 1rem;
          animation: ctaIconBounce 2s ease-in-out infinite;
        }

        h3 {
          font-size: 1.3rem;
          font-weight: 700;
          color: white;
          margin: 0 0 0.5rem 0;
          position: relative;
          z-index: 2;
        }

        p {
          font-size: 1rem;
          opacity: 0.9;
          margin: 0 0 1.5rem 0;
          position: relative;
          z-index: 2;
        }

        .cta-features {
          display: flex;
          justify-content: center;
          gap: 1rem;
          flex-wrap: wrap;
          position: relative;
          z-index: 2;

          .cta-feature {
            font-size: 0.85rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 500;
          }
        }
      }
    }

    // 底部装饰
    .bottom-decoration {
      margin-top: 3rem;
      position: relative;
      height: 60px;

      .wave-animation {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 40px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        border-radius: 20px 20px 0 0;
        animation: waveFlow 4s ease-in-out infinite;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .register-container {
    flex-direction: column;
  }

  .register-card {
    max-width: none;
    min-height: 100vh;
  }

  .showcase-section {
    min-height: 60vh;
    padding: 2rem;

    .showcase-content {
      .showcase-header {
        .title-container {
          .animated-title {
            font-size: 2.2rem;
          }
        }
      }

      .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .feature-card {
          padding: 1.25rem;
        }
      }

      .stats-section {
        .stats-container {
          grid-template-columns: repeat(2, 1fr);
          padding: 1.5rem 1rem;

          .stat-item {
            .stat-number {
              font-size: 1.6rem;
            }
          }
        }
      }

      .realtime-section {
        .realtime-grid {
          grid-template-columns: 1fr;
          gap: 0.75rem;
        }
      }

      .tech-stack-section {
        .tech-stack-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      .testimonials-carousel {
        .testimonials-container {
          height: 220px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .register-card {
    padding: 2rem 1.5rem;

    .back-login {
      top: 1rem;
      left: 1rem;
    }

    .register-header {
      margin-bottom: 2rem;

      .logo-section {
        .logo-icon {
          width: 60px;
          height: 60px;
        }

        h1 {
          font-size: 1.8rem;
        }
      }
    }
  }

  .showcase-section {
    padding: 1.5rem;

    .geometric-shapes {
      .shape {
        &.shape-1 {
          width: 60px;
          height: 60px;
        }

        &.shape-2 {
          width: 40px;
          height: 40px;
        }

        &.shape-3 {
          width: 50px;
          height: 50px;
        }

        &.shape-4 {
          width: 80px;
          height: 30px;
        }
      }
    }

    .showcase-content {
      .showcase-header {
        margin-bottom: 2rem;

        .title-container {
          .animated-title {
            font-size: 2rem;
          }

          .subtitle-container {
            .animated-subtitle {
              font-size: 1rem;
            }
          }
        }
      }

      .features-grid {
        .feature-card {
          padding: 1rem;

          .feature-icon-container {
            width: 48px;
            height: 48px;
          }

          .feature-content {
            h3 {
              font-size: 1rem;
            }

            p {
              font-size: 0.85rem;
            }
          }
        }
      }

      .stats-section {
        .stats-container {
          grid-template-columns: repeat(2, 1fr);
          gap: 0.75rem;
          padding: 1.25rem;

          .stat-item {
            .stat-icon {
              font-size: 1.2rem;
            }

            .stat-number {
              font-size: 1.4rem;
            }

            .stat-label {
              font-size: 0.75rem;
            }
          }
        }
      }

      .realtime-section {
        .realtime-grid {
          .realtime-item {
            padding: 0.75rem;

            .realtime-icon {
              font-size: 1rem;
            }

            .realtime-content {
              .realtime-number {
                font-size: 1rem;
              }

              .realtime-label {
                font-size: 0.65rem;
              }
            }
          }
        }
      }

      .tech-stack-section {
        .tech-stack-grid {
          .tech-item {
            padding: 0.75rem;

            .tech-icon {
              font-size: 1.2rem;
            }

            .tech-name {
              font-size: 0.75rem;
            }
          }
        }
      }

      .testimonials-carousel {
        .carousel-header {
          .section-title {
            font-size: 1rem;
          }

          .carousel-controls {
            .carousel-btn {
              width: 28px;
              height: 28px;
            }
          }
        }

        .testimonials-container {
          height: 180px;

          .testimonial-card {
            padding: 1.25rem;

            .testimonial-content {
              .testimonial-text {
                font-size: 0.9rem;
                margin-bottom: 0.75rem;
              }

              .testimonial-author {
                .author-avatar {
                  width: 40px;
                  height: 40px;
                  font-size: 1rem;
                }

                .author-info {
                  .author-name {
                    font-size: 0.85rem;
                  }

                  .author-title {
                    font-size: 0.75rem;
                  }

                  .rating {
                    .star {
                      font-size: 0.75rem;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .cta-section {
        .cta-content {
          padding: 1.5rem;

          .cta-icon {
            font-size: 2rem;
          }

          h3 {
            font-size: 1.1rem;
          }

          p {
            font-size: 0.9rem;
          }

          .cta-features {
            gap: 0.5rem;

            .cta-feature {
              font-size: 0.75rem;
              padding: 0.3rem 0.6rem;
            }
          }
        }
      }
    }
  }
}
</style>
